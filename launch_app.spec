# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['launch_app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('main.py', '.'),
        ('storyboard.py', '.'),
        ('.streamlit/config.toml', '.streamlit/'),
        ('.env', '.'),
    ],
    hiddenimports=['webview', 'streamlit'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='StoryboardGenerator',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
app = BUNDLE(
    exe,
    name='StoryboardGenerator.app',
    icon=None,
    bundle_identifier='com.yourcompany.storyboard',
    info_plist={
        'CFBundleDisplayName': 'Storyboard Generator',
        'CFBundleShortVersionString': '1.0.0',
        'NSHighResolutionCapable': True,
    }
)
