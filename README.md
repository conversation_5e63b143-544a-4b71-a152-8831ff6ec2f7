# Storyboard Generator

A powerful AI-powered storyboard generator that creates visual presentations from text descriptions using OpenAI's DALL-E 3.

## Features

- **AI-Powered Generation**: Uses OpenAI's DALL-E 3 to create high-quality storyboard images
- **Customizable Styles**: Multiple artistic styles including sketchy, realistic, and graphic novel styles
- **Flexible Layouts**: Support for different aspect ratios (16:9, 1:1, 9:16)
- **PowerPoint Export**: Automatically generates PowerPoint presentations with your storyboard
- **Native macOS App**: Packaged as a native macOS application with a clean, intuitive interface
- **Progress Tracking**: Real-time progress updates during generation

## Requirements

- macOS (for the packaged app)
- OpenAI API key
- Internet connection

## Installation

### Option 1: Use the Pre-built App (Recommended)

1. Download the `StoryboardGenerator.dmg` file
2. Double-click to mount the DMG
3. Drag the `Storyboard Generator.app` to your Applications folder
4. Launch the app from Applications or Launchpad

### Option 2: Run from Source

1. Clone this repository:
   ```bash
   git clone <repository-url>
   cd storyboard
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Create a `.env` file with your OpenAI API key:
   ```
   OPENAI_API_KEY=your_api_key_here
   ```

4. Run the application:
   ```bash
   python3 launch_app.py
   ```

## Configuration

### API Key Setup

The app requires an OpenAI API key to function. You can set this up in two ways:

1. **Environment Variable**: Set `OPENAI_API_KEY` in your environment
2. **`.env` File**: Create a `.env` file in the app directory with:
   ```
   OPENAI_API_KEY=your_api_key_here
   ```

### Supported Image Models

- **OpenAI DALL-E 3**: High-quality image generation with excellent prompt following

### Available Styles

- **Sketchy B&W Graphic**: Black and white sketchy style, perfect for storyboards
- **Realistic**: Photorealistic style for more detailed visualizations
- **Graphic Novel**: Comic book/graphic novel style
- **Minimalist**: Clean, simple style with minimal details
- **Watercolor**: Artistic watercolor painting style

### Aspect Ratios

- **16:9 (Widescreen)**: Perfect for presentations and video storyboards
- **1:1 (Square)**: Great for social media content
- **9:16 (Vertical)**: Ideal for mobile content and stories

## Usage

1. **Launch the App**: Open Storyboard Generator from your Applications folder

2. **Enter Description**: Provide a detailed description of your story or scene

3. **Set Expectations** (Optional): Add specific requirements or expectations for each scene

4. **Configure Settings**:
   - Choose your preferred image model
   - Set the number of steps (or leave empty for automatic)
   - Select aspect ratio
   - Choose artistic style
   - Add style description for customization
   - Specify terms to avoid

5. **Generate**: Click "Generate Storyboard" and wait for the AI to create your presentation

6. **Download**: Once complete, download your PowerPoint presentation

## Tips for Best Results

- **Be Specific**: Detailed descriptions yield better results
- **Use Visual Language**: Describe scenes, characters, and actions clearly
- **Consider Continuity**: Maintain consistent character and setting descriptions
- **Experiment with Styles**: Different styles work better for different types of content

## Troubleshooting

### Common Issues

- **API Key Error**: Ensure your OpenAI API key is correctly set in the `.env` file
- **Generation Fails**: Check your internet connection and API key validity
- **App Won't Launch**: Make sure you're running macOS and have moved the app to Applications

### Getting Help

If you encounter issues:
1. Check the console output for error messages
2. Verify your OpenAI API key is valid and has sufficient credits
3. Ensure you have a stable internet connection

## Development

### Building from Source

To build the macOS app from source:

1. Install PyInstaller:
   ```bash
   pip install pyinstaller
   ```

2. Build the app:
   ```bash
   pyinstaller launch_app.spec
   ```

3. The app will be created in the `dist/` directory

### Creating a DMG

Use the included `create_dmg.sh` script:
```bash
./create_dmg.sh
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- OpenAI for the DALL-E 3 API
- Streamlit for the web interface framework
- PyInstaller for app packaging
