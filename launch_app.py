import subprocess
import sys
import time
import threading
import requests
from pathlib import Path

import webview

def start_streamlit():
    """Start Streamlit server in background"""
    subprocess.Popen([
        sys.executable, "-m", "streamlit", "run", "main.py",
        "--server.headless=true",
        "--server.port=8501",
        "--browser.gatherUsageStats=false"
    ])

def wait_for_server():
    """Wait for Streamlit server to be ready"""
    for _ in range(30):  # 30 second timeout
        try:
            response = requests.get("http://localhost:8501", timeout=1)
            if response.status_code == 200:
                return True
        except:
            time.sleep(1)
    return False

def main():
    # Start Streamlit in background
    start_streamlit()
    
    # Wait for server to be ready
    if not wait_for_server():
        print("Failed to start Streamlit server")
        return
    
    # Create native window with embedded web view
    webview.create_window(
        title="Storyboard Generator",
        url="http://localhost:8501",
        width=1200,
        height=800,
        resizable=True,
        fullscreen=False
    )
    
    webview.start(debug=False)

if __name__ == "__main__":
    main()
