# Storyboard Generator - Installation Guide

## Quick Start

1. **Download** the `StoryboardGenerator.dmg` file
2. **Double-click** the DMG file to mount it
3. **Drag** the `StoryboardGenerator.app` to your Applications folder
4. **Set up** your OpenAI API key (see below)
5. **Launch** the app from Applications or Launchpad

## Setting Up Your OpenAI API Key

The Storyboard Generator requires an OpenAI API key to generate images. Here's how to set it up:

### Step 1: Get an OpenAI API Key

1. Visit [OpenAI's API Keys page](https://platform.openai.com/api-keys)
2. Sign in to your OpenAI account (or create one if needed)
3. Click "Create new secret key"
4. Copy the generated API key (it starts with `sk-`)
5. Make sure you have credits in your OpenAI account

### Step 2: Configure the API Key

You have two options to set up your API key:

#### Option A: Environment Variable (Recommended)

1. Open **Terminal** (found in Applications > Utilities)
2. Run this command, replacing `your_api_key_here` with your actual API key:
   ```bash
   echo 'export OPENAI_API_KEY="your_api_key_here"' >> ~/.zshrc
   ```
3. Restart Terminal or run: `source ~/.zshrc`
4. Launch the Storyboard Generator app

#### Option B: .env File

1. Navigate to where you installed the app (usually Applications folder)
2. Right-click on `StoryboardGenerator.app` and select "Show Package Contents"
3. Navigate to `Contents/MacOS/`
4. Create a new file called `.env` (note the dot at the beginning)
5. Add this line to the file:
   ```
   OPENAI_API_KEY=your_api_key_here
   ```
6. Save the file and launch the app

## First Launch

### macOS Security Notice

When you first launch the app, macOS might show a security warning because the app isn't signed with an Apple Developer certificate.

**To open the app:**
1. Right-click on `StoryboardGenerator.app`
2. Select "Open" from the context menu
3. Click "Open" in the security dialog
4. The app will launch and remember this choice for future launches

### Verify Installation

Once the app launches, you should see:
- A clean, modern interface
- Settings panel on the left side
- Main content area for entering your story description
- Generate button to create storyboards

## Troubleshooting

### App Won't Launch
- Make sure you've moved the app to the Applications folder
- Try the right-click > Open method described above
- Check Console.app for any error messages

### API Key Issues
- Verify your API key is correct (starts with `sk-`)
- Ensure you have credits in your OpenAI account
- Check that the environment variable is set: `echo $OPENAI_API_KEY` in Terminal

### Generation Fails
- Check your internet connection
- Verify your OpenAI account has sufficient credits
- Try with a simpler description first

### Performance Issues
- The app requires internet connectivity for AI generation
- Generation time depends on the number of steps and complexity
- Larger storyboards take longer to generate

## Getting Help

If you encounter issues:

1. **Check the README.md** file in the DMG for detailed usage instructions
2. **Verify your setup** using the troubleshooting steps above
3. **Check your OpenAI account** for API key validity and credits
4. **Try a simple test** with a basic story description

## System Requirements

- **macOS**: 10.14 (Mojave) or later
- **Internet**: Required for AI image generation
- **OpenAI Account**: With valid API key and credits
- **Storage**: ~100MB for the app, additional space for generated presentations

## Uninstalling

To remove the Storyboard Generator:
1. Drag `StoryboardGenerator.app` from Applications to Trash
2. Remove the environment variable (if used): edit `~/.zshrc` and remove the OPENAI_API_KEY line
3. Empty Trash

---

**Enjoy creating amazing storyboards with AI! 🎬✨**
