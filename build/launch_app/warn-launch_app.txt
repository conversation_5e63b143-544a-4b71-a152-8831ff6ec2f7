
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.unquote - imported by urllib (conditional), bottle (conditional)
missing module named urllib.quote - imported by urllib (conditional), bottle (conditional)
missing module named urllib.urlencode - imported by urllib (conditional), bottle (conditional), lxml.html (delayed, optional), requests_toolbelt._compat (conditional)
missing module named _winapi - imported by ntpath (optional), encodings (delayed, conditional, optional), shutil (conditional), subprocess (conditional), multiprocessing.connection (optional), multiprocessing.spawn (delayed, conditional), multiprocessing.reduction (conditional), multiprocessing.shared_memory (conditional), multiprocessing.heap (conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level), mimetypes (optional), sysconfig (delayed)
missing module named winreg - imported by importlib._bootstrap_external (conditional), platform (delayed, optional), mimetypes (optional), urllib.request (delayed, conditional, optional), requests.utils (delayed, conditional, optional), webview.platforms.winforms (top-level), webview.platforms.cef (delayed), webview.platforms.edgechromium (top-level), webview.platforms.mshtml (delayed), setuptools._distutils.compilers.C.msvc (top-level), setuptools.msvc (conditional)
missing module named nt - imported by os (delayed, conditional, optional), ntpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), _colorize (delayed, conditional, optional), ctypes (delayed, conditional), _pyrepl.windows_console (delayed, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named msvcrt - imported by subprocess (optional), multiprocessing.spawn (delayed, conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level), getpass (optional), click._winconsole (top-level), click._termui_impl (conditional), _pyrepl.windows_console (top-level)
missing module named _overlapped - imported by asyncio.windows_events (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), langsmith._internal._background_thread (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional), attr._compat (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _wmi - imported by platform (optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named _typeshed - imported by tornado.wsgi (conditional), numpy.random.bit_generator (top-level), streamlit.runtime.state.query_params (conditional), git.objects.fun (conditional), streamlit.runtime.state.query_params_proxy (conditional), pydantic_core._pydantic_core (top-level), pydantic._internal._dataclasses (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), anyio._core._tempfile (conditional), httpx._transports.wsgi (conditional), setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named importlib_resources - imported by jsonschema_specifications._core (optional), tqdm.cli (delayed, conditional, optional), setuptools._vendor.jaraco.text (optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named pyimod02_importers - imported by /Users/<USER>/Desktop/AI Tutorials/storyboard/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py (delayed)
missing module named 'IPython.core' - imported by pandas.io.formats.printing (delayed, conditional), altair.utils.core (delayed, conditional), altair._magics (top-level), dotenv.ipython (top-level)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named 'trio.testing' - imported by anyio._backends._trio (delayed)
missing module named exceptiongroup - imported by anyio._core._exceptions (conditional), anyio._core._sockets (conditional), anyio._backends._asyncio (conditional), anyio._backends._trio (conditional)
missing module named 'trio.to_thread' - imported by anyio._backends._trio (top-level)
missing module named 'trio.socket' - imported by anyio._backends._trio (top-level)
missing module named outcome - imported by anyio._backends._trio (top-level)
missing module named 'trio.lowlevel' - imported by anyio._backends._trio (top-level)
missing module named 'trio.from_thread' - imported by anyio._backends._trio (top-level)
missing module named _pytest - imported by anyio._backends._asyncio (delayed)
missing module named uvloop - imported by bottle (delayed), anyio._backends._asyncio (delayed, conditional)
missing module named jiter.from_json - imported by jiter (top-level), openai.lib.streaming.chat._completions (top-level)
missing module named trio - imported by tenacity.asyncio (delayed, conditional), httpx._transports.asgi (delayed, conditional), httpcore._synchronization (optional), httpcore._backends.trio (top-level), openai.resources.vector_stores.file_batches (delayed, conditional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), pydantic.v1._hypothesis_plugin (optional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic._internal._core_utils (delayed), pydantic.deprecated.copy_internals (delayed, conditional), openai.resources.beta.realtime.realtime (top-level), langchain_core.language_models.base (top-level), langchain_core.load.dump (top-level), langchain_core.load.serializable (top-level), langchain_core.utils.pydantic (top-level), langchain_core.prompts.base (top-level), langchain_core.runnables.base (top-level), langchain_core.documents.compressor (top-level), langsmith.schemas (optional), langsmith.evaluation.evaluator (optional), langsmith.evaluation.string_evaluator (top-level), langchain_core.prompts.structured (top-level), langchain_core.prompts.string (top-level), langchain_core.prompts.prompt (top-level), langchain_core.runnables.graph (conditional), langchain_core.runnables.fallbacks (top-level), langchain_core.tools.base (top-level), langchain_core.utils.function_calling (top-level), langchain_core.tools.convert (top-level), langchain_core.tools.retriever (top-level), langchain_core.runnables.passthrough (top-level), langchain_core.runnables.configurable (top-level), langchain_core.runnables.branch (top-level), langchain_core.runnables.history (top-level), langchain_core.chat_history (top-level), langchain_core.prompts.few_shot (top-level), langchain_core.example_selectors.length_based (top-level), langchain_core.example_selectors.semantic_similarity (top-level), langchain_core.embeddings.fake (top-level), langchain_core.outputs.chat_result (top-level), langchain_core.outputs.llm_result (top-level), langchain_core.outputs.run_info (top-level), langchain_core.language_models.chat_models (top-level), langchain_openai.chat_models.azure (top-level), langchain_openai.chat_models.base (top-level), langchain_openai.embeddings.base (top-level)
missing module named 'rich.pretty' - imported by pydantic._internal._core_utils (delayed)
missing module named 'rich.console' - imported by streamlit.error_util (delayed), pydantic._internal._core_utils (conditional), httpx._main (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named cython - imported by pydantic.v1.version (optional), lxml.html.diff (optional), lxml.html._difflib (optional), pyarrow.conftest (optional)
missing module named sounddevice - imported by openai._extras.sounddevice_proxy (delayed, conditional, optional)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named six.moves.winreg - imported by six.moves (top-level), dateutil.tz.win (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named StringIO - imported by bottle (conditional), six (conditional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core.groupby.numba_ (delayed, conditional), pandas.core._numba.extensions (top-level)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named sphinx - imported by pyarrow.vendored.docscrape (delayed, conditional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named 'pandas.api.internals' - imported by pyarrow.pandas_compat (delayed, conditional)
missing module named 'pyarrow._cuda' - imported by pyarrow.cuda (top-level)
missing module named 'pyarrow.gandiva' - imported by pyarrow.conftest (optional)
missing module named fastparquet - imported by pyarrow.conftest (optional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional), langsmith.env._runtime_env (optional), pyarrow.tests.util (delayed)
missing module named pytest - imported by pandas._testing._io (delayed), pandas._testing (delayed), langsmith.testing._internal (optional), pyarrow.conftest (top-level), pyarrow.tests.util (top-level)
missing module named cffi - imported by pyarrow.cffi (top-level)
missing module named fsspec - imported by pyarrow.fs (delayed, conditional, optional), pandas.io.orc (conditional)
missing module named 'setuptools_scm.git' - imported by pyarrow (delayed, optional)
missing module named setuptools_scm - imported by pyarrow (optional), tqdm.version (optional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._array_utils_impl (top-level), numpy (conditional), numpy.fft._helper (top-level), numpy.fft._pocketfft (top-level)
missing module named threadpoolctl - imported by numpy.lib._utils_impl (delayed, optional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._function_base_impl (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy.lib._utils_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longlong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named 'scipy.stats' - imported by pandas.core.nanops (delayed, conditional)
missing module named scipy - imported by pandas.core.dtypes.common (delayed, conditional, optional), pandas.core.missing (delayed)
missing module named traitlets - imported by pydeck.widget.widget (top-level), pandas.io.formats.printing (delayed, conditional), altair.jupyter.jupyter_chart (top-level)
missing module named IPython - imported by pydeck.io.html (delayed), pandas.io.formats.printing (delayed)
missing module named 'openpyxl.cell' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.styles' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.workbook' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.descriptors' - imported by pandas.io.excel._openpyxl (conditional)
missing module named openpyxl - imported by pandas.io.excel._openpyxl (delayed, conditional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed, conditional), pandas.io.excel._base (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named 'matplotlib.pyplot' - imported by pandas.io.formats.style (optional), streamlit.elements.pyplot (delayed, optional), tqdm.gui (delayed)
missing module named matplotlib - imported by pandas.plotting._core (conditional), pandas.io.formats.style (optional), streamlit.elements.plotly_chart (conditional), tqdm.gui (delayed)
missing module named 'matplotlib.colors' - imported by pandas.plotting._misc (conditional), pandas.io.formats.style (conditional)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional), requests_toolbelt._compat (conditional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named 'scipy.sparse' - imported by pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (delayed, conditional), pandas.core.arrays.sparse.accessor (delayed)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named qtpy - imported by webview.platforms.qt (top-level), pandas.io.clipboard (delayed, conditional, optional)
missing module named Foundation.NSTimer - imported by Foundation (top-level), PyObjCTools.AppHelper (top-level)
missing module named Foundation.NSThread - imported by Foundation (top-level), PyObjCTools.AppHelper (top-level)
missing module named Foundation.NSRunLoop - imported by Foundation (top-level), PyObjCTools.AppHelper (top-level)
missing module named Foundation.NSObject - imported by Foundation (top-level), PyObjCTools.AppHelper (top-level)
missing module named Foundation.NSNotificationCenter - imported by Foundation (top-level), PyObjCTools.AppHelper (top-level)
missing module named Foundation.NSLog - imported by Foundation (top-level), PyObjCTools.AppHelper (top-level)
missing module named Foundation.NSDefaultRunLoopMode - imported by Foundation (top-level), PyObjCTools.AppHelper (top-level)
missing module named Foundation.NSDate - imported by Foundation (top-level), PyObjCTools.AppHelper (top-level)
missing module named Foundation.NSAutoreleasePool - imported by Foundation (top-level), PyObjCTools.AppHelper (top-level)
missing module named objc.super - imported by objc (top-level), webview.platforms.cocoa (top-level), PyObjCTools.AppHelper (top-level)
missing module named objc.nosuchclass_error - imported by objc (top-level), objc._lazyimport (top-level)
missing module named objc.loadBundle - imported by objc (top-level), objc._lazyimport (top-level)
missing module named objc.getClassList - imported by objc (top-level), objc._lazyimport (top-level)
missing module named objc.NULL - imported by objc (top-level), objc._properties (top-level)
missing module named objc._C_BOOL - imported by objc (top-level), objc._properties (top-level)
missing module named AppKit.NSRunAlertPanel - imported by AppKit (top-level), PyObjCTools.AppHelper (top-level)
missing module named AppKit.NSApplicationMain - imported by AppKit (top-level), PyObjCTools.AppHelper (top-level)
missing module named AppKit.NSApplicationDidFinishLaunchingNotification - imported by AppKit (top-level), PyObjCTools.AppHelper (top-level)
missing module named AppKit.NSApp - imported by AppKit (top-level), PyObjCTools.AppHelper (top-level)
missing module named PyObjCTools.Debugging - imported by PyObjCTools (delayed, conditional), PyObjCTools.AppHelper (delayed, conditional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'qtpy.QtWidgets' - imported by webview.platforms.qt (top-level), pandas.io.clipboard (delayed, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed), streamlit.connections.sql_connection (conditional)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.sql' - imported by pandas.io.sql (conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional), streamlit.connections.sql_connection (delayed)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named bs4 - imported by pandas.io.html (delayed), lxml.html.soupparser (optional)
missing module named urlparse - imported by bottle (conditional), requests_toolbelt._compat (conditional), lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named 'html5lib.treebuilders' - imported by lxml.html.html5parser (top-level)
missing module named html5lib - imported by lxml.html._html5builder (top-level), lxml.html.html5parser (top-level)
missing module named 'cython.cimports' - imported by lxml.html.diff (optional)
missing module named lxml_html_clean - imported by lxml.html.clean (optional)
missing module named cgi - imported by lxml.doctestcompare (optional)
missing module named cssselect - imported by lxml.cssselect (optional)
missing module named 'google.auth' - imported by pandas.io.gbq (conditional)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named numpy.random.RandomState - imported by numpy.random (top-level), numpy.random._generator (top-level)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named 'matplotlib.axes' - imported by pandas.plotting._misc (conditional), pandas._testing.asserters (delayed)
missing module named 'matplotlib.artist' - imported by pandas._testing.asserters (delayed)
missing module named 'matplotlib.table' - imported by pandas.plotting._misc (conditional)
missing module named 'matplotlib.figure' - imported by pandas.plotting._misc (conditional), streamlit.elements.pyplot (conditional)
missing module named 'websockets.exceptions' - imported by openai.resources.beta.realtime.realtime (delayed)
missing module named 'websockets.asyncio' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional)
missing module named 'websockets.sync' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional)
missing module named 'websockets.extensions' - imported by openai.types.websocket_connection_options (conditional)
missing module named websockets - imported by openai.types.websocket_connection_options (conditional)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.config' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'rich.table' - imported by httpx._main (top-level)
missing module named 'rich.syntax' - imported by httpx._main (top-level)
missing module named 'rich.progress' - imported by httpx._main (top-level)
missing module named 'rich.markup' - imported by httpx._main (top-level)
missing module named 'pygments.util' - imported by httpx._main (top-level)
missing module named pygments - imported by httpx._main (top-level)
missing module named colorama - imported by tornado.log (optional), click._compat (delayed, conditional), tqdm.utils (conditional, optional)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named zstandard.backend_rust - imported by zstandard (conditional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional)
missing module named h2 - imported by urllib3.http2.connection (top-level), httpx._client (delayed, conditional, optional)
missing module named httpx_aiohttp - imported by openai._base_client (optional)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional), webview (delayed)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional), webview (delayed)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named orjson.loads - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.dumps - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.JSONDecodeError - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.Fragment - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_SERIALIZE_UUID - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_SERIALIZE_NUMPY - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_SERIALIZE_DATACLASS - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_NON_STR_KEYS - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named vcr - imported by langsmith.utils (delayed, optional)
missing module named 'opentelemetry.trace' - imported by langsmith.run_helpers (delayed), langsmith.client (conditional, optional), langsmith._internal.otel._otel_exporter (conditional, optional)
missing module named opentelemetry - imported by langsmith.run_helpers (delayed, conditional, optional), langsmith._internal._background_thread (delayed, optional), langsmith.client (conditional, optional), langsmith._internal.otel._otel_exporter (conditional, optional)
missing module named langchain - imported by langsmith.evaluation.integrations._langchain (conditional), langsmith.env._runtime_env (delayed, optional), langchain_core.globals (delayed, optional)
missing module named langchain_anthropic - imported by langsmith.client (delayed, optional)
missing module named simsimd - imported by langsmith._internal._embedding_distance (delayed, optional), langchain_core.vectorstores.utils (delayed, optional)
missing module named defusedxml - imported by PIL.Image (optional), langchain_core.output_parsers.xml (delayed, conditional, optional)
missing module named 'defusedxml.ElementTree' - imported by langchain_core.output_parsers.xml (delayed, conditional, optional)
missing module named collections.MutableSequence - imported by collections (optional), jsonpatch (optional)
missing module named collections.MutableMapping - imported by collections (conditional), bottle (conditional), requests_toolbelt._compat (conditional), jsonpatch (optional)
missing module named collections.Sequence - imported by collections (optional), jsonpatch (optional)
missing module named transformers - imported by langchain_core.language_models.base (delayed, optional), langchain_openai.embeddings.base (delayed, conditional, optional)
missing module named 'langchain.smith' - imported by langsmith.client (delayed, optional)
missing module named langsmith_pyo3 - imported by langsmith.client (delayed, conditional, optional)
missing module named 'IPython.display' - imported by pydeck.io.html (delayed), altair.vegalite.v5.display (delayed), altair.vegalite.v5.api (delayed, conditional), tqdm.notebook (conditional, optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by pydeck.widget.widget (top-level), tqdm.notebook (conditional, optional)
missing module named 'langchain.evaluation' - imported by langsmith.evaluation.integrations._langchain (delayed, conditional)
missing module named 'opentelemetry.sdk' - imported by langsmith._internal.otel._otel_client (conditional, optional), langsmith.client (optional)
missing module named 'opentelemetry.context' - imported by langsmith._internal._background_thread (conditional), langsmith._internal.otel._otel_exporter (conditional, optional)
missing module named 'opentelemetry.exporter' - imported by langsmith._internal.otel._otel_client (conditional, optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named Queue - imported by requests_toolbelt._compat (conditional)
missing module named 'requests.packages.urllib3' - imported by requests_toolbelt._compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named rapidfuzz - imported by langsmith._internal._edit_distance (delayed, optional)
missing module named pydantic.validate_arguments - imported by pydantic (top-level), langchain_core.tools.base (top-level)
missing module named pyppeteer - imported by langchain_core.runnables.graph_mermaid (delayed, optional)
missing module named pygraphviz - imported by langchain_core.runnables.graph_png (delayed, optional)
missing module named 'grandalf.routing' - imported by langchain_core.runnables.graph_ascii (delayed, optional)
missing module named 'grandalf.layouts' - imported by langchain_core.runnables.graph_ascii (delayed, optional)
missing module named grandalf - imported by langchain_core.runnables.graph_ascii (delayed, optional)
missing module named langchain_text_splitters - imported by langchain_core.document_loaders.base (delayed, conditional, optional), langchain_core.messages.utils (delayed, conditional, optional)
missing module named regex.DEFAULT_VERSION - imported by regex (delayed, optional), regex.regex (delayed, optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named 'mypy.version' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.util' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.typevars' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.types' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.server' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.semanal' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugins' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugin' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.options' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.nodes' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by pydantic.v1.mypy (top-level)
missing module named hypothesis - imported by pydantic.v1._hypothesis_plugin (top-level)
missing module named 'mypy.typeops' - imported by pydantic.mypy (top-level)
missing module named 'mypy.type_visitor' - imported by pydantic.mypy (top-level)
missing module named 'mypy.state' - imported by pydantic.mypy (top-level)
missing module named 'mypy.expandtype' - imported by pydantic.mypy (top-level)
missing module named mypy - imported by pydantic.mypy (top-level)
missing module named rpds.List - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieSet - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieMap - imported by rpds (top-level), referencing._core (top-level), jsonschema._types (top-level), jsonschema.validators (top-level)
missing module named polars - imported by streamlit.dataframe_util (delayed, conditional), streamlit.runtime.caching.hashing (delayed, conditional), narwhals.dependencies (conditional), narwhals._utils (conditional), narwhals.series (conditional), narwhals._arrow.dataframe (delayed, conditional), narwhals.schema (delayed, conditional), narwhals._pandas_like.series (delayed, conditional), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._polars.dataframe (top-level), narwhals._polars.namespace (top-level), narwhals._polars.expr (top-level), narwhals._polars.utils (top-level), narwhals._polars.series (top-level), narwhals.stable.v1.dependencies (conditional), narwhals._dask.dataframe (delayed, conditional), narwhals._arrow.series (delayed, conditional), narwhals.dataframe (conditional), narwhals._compliant.series (conditional), narwhals._compliant.dataframe (conditional), narwhals._namespace (conditional), narwhals._spark_like.dataframe (delayed, conditional)
missing module named 'sqlframe.base' - imported by narwhals._spark_like.utils (delayed, conditional), narwhals._spark_like.expr_dt (conditional), narwhals._spark_like.expr_str (conditional), narwhals._spark_like.expr_struct (conditional), narwhals._spark_like.expr (delayed, conditional), narwhals._spark_like.selectors (conditional), narwhals._spark_like.namespace (delayed, conditional), narwhals._spark_like.dataframe (delayed, conditional), narwhals._spark_like.group_by (conditional), narwhals.dependencies (delayed, conditional)
missing module named 'pyspark.errors' - imported by narwhals._spark_like.utils (delayed)
missing module named 'ibis.selectors' - imported by narwhals._ibis.dataframe (delayed)
missing module named 'ibis.common' - imported by narwhals._ibis.utils (conditional)
missing module named 'ibis.expr' - imported by narwhals._ibis.dataframe (top-level), narwhals._ibis.utils (top-level), narwhals._ibis.expr_dt (conditional), narwhals._ibis.expr_str (top-level), narwhals._ibis.expr_struct (conditional), narwhals._ibis.expr (conditional), narwhals._ibis.namespace (top-level), narwhals._ibis.selectors (conditional), narwhals._ibis.group_by (conditional)
missing module named ibis - imported by narwhals.dependencies (conditional), narwhals.stable.v1.dependencies (conditional), narwhals._polars.dataframe (delayed, conditional), narwhals._ibis.dataframe (top-level), narwhals._ibis.utils (top-level), narwhals._ibis.expr (top-level), narwhals._ibis.expr_str (top-level), narwhals._ibis.namespace (top-level), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._arrow.dataframe (delayed, conditional)
missing module named dask_expr - imported by narwhals._dask.utils (conditional, optional), narwhals._dask.group_by (conditional, optional)
missing module named 'dask.array' - imported by narwhals._dask.expr (delayed)
missing module named 'pyarrow._stubs_typing' - imported by narwhals._arrow.typing (conditional)
missing module named 'pyarrow.__lib_pxi' - imported by narwhals._arrow.typing (conditional)
missing module named cupy - imported by narwhals._pandas_like.utils (delayed, conditional)
missing module named 'dask.dataframe' - imported by narwhals.stable.v1.dependencies (conditional), narwhals._polars.dataframe (delayed, conditional), narwhals._dask.dataframe (top-level), narwhals._dask.utils (conditional, optional), narwhals._dask.expr_dt (conditional), narwhals._dask.expr_str (top-level), narwhals._dask.expr (conditional), narwhals._dask.namespace (top-level), narwhals._dask.selectors (conditional), narwhals._dask.group_by (top-level), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._arrow.dataframe (delayed, conditional)
missing module named 'duckdb.typing' - imported by narwhals._duckdb.utils (conditional), narwhals._duckdb.expr (top-level), narwhals._duckdb.namespace (top-level), narwhals._duckdb.dataframe (conditional)
missing module named 'polars.lazyframe' - imported by narwhals._polars.group_by (conditional)
missing module named 'polars.dataframe' - imported by narwhals._polars.group_by (conditional)
missing module named 'modin.pandas' - imported by narwhals._pandas_like.dataframe (delayed, conditional), narwhals.stable.v1.dependencies (conditional)
missing module named 'pyspark.sql' - imported by narwhals.dependencies (delayed, conditional, optional), narwhals._namespace (conditional), narwhals._spark_like.utils (delayed, conditional)
missing module named duckdb - imported by narwhals.dependencies (conditional), narwhals._polars.dataframe (delayed, conditional), narwhals._duckdb.dataframe (top-level), narwhals._duckdb.utils (top-level), narwhals._duckdb.expr (top-level), narwhals._duckdb.expr_dt (conditional), narwhals._duckdb.expr_str (conditional), narwhals._duckdb.namespace (top-level), narwhals._duckdb.selectors (conditional), narwhals._duckdb.typing (conditional), narwhals._duckdb.group_by (conditional), narwhals._duckdb.series (conditional), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._arrow.dataframe (delayed, conditional), narwhals._namespace (conditional)
missing module named pyspark - imported by narwhals.dependencies (conditional), narwhals._utils (delayed, conditional)
missing module named modin - imported by narwhals.dependencies (conditional)
missing module named dask - imported by narwhals.dependencies (conditional), narwhals._utils (delayed, conditional)
missing module named cudf - imported by narwhals.dependencies (conditional), narwhals.stable.v1.dependencies (conditional)
missing module named sqlframe - imported by narwhals._utils (delayed, conditional)
missing module named isoduration - imported by jsonschema._format (top-level)
missing module named uri_template - imported by jsonschema._format (top-level)
missing module named webcolors - imported by jsonschema._format (top-level)
missing module named rfc3339_validator - imported by jsonschema._format (top-level)
missing module named rfc3986_validator - imported by jsonschema._format (optional)
missing module named rfc3987 - imported by jsonschema._format (optional)
missing module named fqdn - imported by jsonschema._format (top-level)
missing module named 'vegafusion.runtime' - imported by altair.utils._vegafusion_data (conditional)
missing module named altair.vegalite.SCHEMA_VERSION - imported by altair.vegalite (delayed), altair.utils._importers (delayed)
missing module named vl_convert - imported by altair.utils._importers (delayed, optional)
missing module named vegafusion - imported by altair.utils._importers (delayed, optional)
missing module named altair.vegalite.v5.SCHEMA_VERSION - imported by altair.vegalite.v5 (delayed), altair.vegalite.v5.compiler (delayed)
missing module named anywidget - imported by altair.jupyter (optional), altair.jupyter.jupyter_chart (top-level)
missing module named altair.VConcatSpecGenericSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.VConcatChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.UnitSpecWithFrame - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.UnitSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelVConcatSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelUnitSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelLayerSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelHConcatSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelFacetSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelConcatSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.NonNormalizedSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.LayerSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.LayerChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.HConcatSpecGenericSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.HConcatChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.FacetSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.FacetedUnitSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.FacetChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.ConcatSpecGenericSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.ConcatChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.Chart - imported by altair (delayed), altair.vegalite.v5.display (delayed), altair.utils._transformed_data (top-level)
missing module named altair.renderers - imported by altair (delayed), altair.utils.mimebundle (delayed)
missing module named altair.vegalite_compilers - imported by altair (delayed), altair.utils._vegafusion_data (delayed)
missing module named altair.data_transformers - imported by altair (delayed), altair.utils._vegafusion_data (delayed), altair.utils._transformed_data (top-level)
missing module named altair.SchemaBase - imported by altair (conditional), altair.vegalite.v5.schema.channels (conditional)
missing module named altair.Parameter - imported by altair (conditional), altair.vegalite.v5.schema.core (conditional), altair.vegalite.v5.schema.channels (conditional), altair.vegalite.v5.schema.mixins (conditional)
missing module named xarray - imported by streamlit.dataframe_util (delayed, conditional)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named tensorflow - imported by streamlit.elements.write (delayed, conditional)
missing module named gitdb_speedups - imported by gitdb.fun (optional)
missing module named 'gitdb_speedups._perf' - imported by gitdb.stream (optional), gitdb.pack (optional)
missing module named sha - imported by gitdb.util (delayed, optional)
missing module named 'watchdog.observers' - imported by streamlit.watcher.event_based_path_watcher (top-level)
missing module named watchdog - imported by streamlit.watcher.path_watcher (delayed, optional), streamlit.watcher.event_based_path_watcher (top-level)
missing module named 'plotly.tools' - imported by streamlit.elements.plotly_chart (delayed)
missing module named 'plotly.io' - imported by streamlit.elements.lib.streamlit_plotly_theme (delayed), streamlit.elements.plotly_chart (delayed)
missing module named 'plotly.basedatatypes' - imported by streamlit.elements.plotly_chart (conditional)
missing module named 'plotly.graph_objs' - imported by streamlit.elements.plotly_chart (conditional)
missing module named 'plotly.graph_objects' - imported by streamlit.elements.lib.streamlit_plotly_theme (delayed)
missing module named 'bokeh.embed' - imported by streamlit.elements.bokeh_chart (delayed)
missing module named bokeh - imported by streamlit.elements.bokeh_chart (delayed, conditional)
missing module named plotly - imported by streamlit.type_util (conditional)
missing module named sympy - imported by streamlit.type_util (delayed, conditional, optional), streamlit.elements.markdown (delayed, conditional)
missing module named graphviz - imported by streamlit.type_util (conditional), streamlit.elements.graphviz_chart (conditional)
missing module named 'rich.panel' - imported by streamlit.error_util (delayed)
missing module named rich - imported by streamlit.error_util (delayed), streamlit.config (delayed, optional)
missing module named 'authlib.integrations' - imported by streamlit.web.server.oidc_mixin (top-level), streamlit.web.server.authlib_tornado_integration (top-level)
missing module named 'authlib.jose' - imported by streamlit.auth_util (delayed, optional)
missing module named authlib - imported by streamlit.auth_util (delayed, optional)
missing module named 'sqlalchemy.exc' - imported by streamlit.connections.sql_connection (delayed)
missing module named 'sqlalchemy.orm' - imported by streamlit.connections.sql_connection (delayed, conditional)
missing module named snowflake - imported by streamlit.connections.util (delayed, optional)
missing module named 'snowflake.snowpark' - imported by streamlit.connections.snowflake_connection (delayed, conditional), streamlit.connections.snowpark_connection (delayed, conditional)
missing module named 'snowflake.connector' - imported by streamlit.connections.snowflake_connection (delayed, conditional)
missing module named WebBrowserInterop - imported by webview.platforms.mshtml (top-level)
missing module named 'System.Windows' - imported by webview.platforms.edgechromium (top-level), webview.platforms.mshtml (top-level)
missing module named System - imported by webview.platforms.winforms (top-level), webview.platforms.edgechromium (top-level), webview.platforms.mshtml (top-level)
missing module named clr - imported by webview.platforms.winforms (top-level), webview.platforms.edgechromium (top-level), webview.platforms.mshtml (top-level)
missing module named 'Microsoft.Web' - imported by webview.platforms.edgechromium (top-level)
missing module named Microsoft - imported by webview.platforms.edgechromium (top-level)
missing module named 'System.Threading' - imported by webview.platforms.winforms (top-level), webview.platforms.edgechromium (top-level)
missing module named 'System.Globalization' - imported by webview.platforms.edgechromium (top-level)
missing module named 'System.Drawing' - imported by webview.platforms.winforms (top-level), webview.platforms.edgechromium (top-level)
missing module named 'System.Diagnostics' - imported by webview.platforms.edgechromium (top-level)
missing module named 'System.Collections' - imported by webview.platforms.edgechromium (top-level)
missing module named cefpython3 - imported by webview.platforms.cef (top-level)
missing module named Security.SecTrustRef - imported by Security (delayed), webview.platforms.cocoa (delayed)
missing module named Security.SecIdentitySearchGetTypeID - imported by Security (delayed)
missing module named Security.SecIdentityGetTypeID - imported by Security (delayed)
missing module named Security.SecAccessGetTypeID - imported by Security (delayed)
missing module named Security.SecCertificateGetTypeID - imported by Security (delayed)
missing module named Security.SecTrustedApplicationGetTypeID - imported by Security (delayed)
missing module named Security.SecAccessControlGetTypeID - imported by Security (delayed, conditional)
missing module named Cheetah - imported by bottle (delayed)
missing module named 'mako.lookup' - imported by bottle (delayed)
missing module named mako - imported by bottle (delayed)
missing module named aiohttp_wsgi - imported by bottle (delayed)
missing module named bjoern - imported by bottle (delayed)
missing module named gunicorn - imported by bottle (delayed)
missing module named diesel - imported by bottle (delayed)
missing module named 'twisted.internet' - imported by bottle (delayed)
missing module named 'twisted.python' - imported by bottle (delayed)
missing module named twisted - imported by bottle (delayed)
missing module named 'google.appengine' - imported by bottle (delayed)
missing module named fapws - imported by bottle (delayed)
missing module named meinheld - imported by bottle (delayed)
missing module named 'paste.translogger' - imported by bottle (delayed)
missing module named paste - imported by bottle (delayed)
missing module named waitress - imported by bottle (delayed)
missing module named 'cheroot.ssl' - imported by bottle (delayed)
missing module named cheroot - imported by bottle (delayed)
missing module named cherrypy - imported by bottle (delayed)
missing module named flup - imported by bottle (delayed)
missing module named ConfigParser - imported by bottle (conditional)
missing module named imp - imported by bottle (conditional)
missing module named cPickle - imported by bottle (conditional)
missing module named Cookie - imported by bottle (conditional)
missing module named thread - imported by bottle (conditional)
missing module named httplib - imported by bottle (conditional)
missing module named ujson - imported by bottle (optional)
missing module named eventlet - imported by bottle (delayed, conditional)
missing module named gevent - imported by bottle (delayed, conditional)
missing module named 'cryptography.hazmat' - imported by webview (delayed)
missing module named 'System.Reflection' - imported by webview.platforms.winforms (top-level)
missing module named 'PyQt5.QtWebKitWidgets' - imported by webview.platforms.qt (optional)
missing module named 'PyQt5.QtNetwork' - imported by webview.platforms.qt (optional)
missing module named PyQt5 - imported by webview.platforms.qt (optional)
missing module named 'qtpy.QtWebEngineWidgets' - imported by webview.platforms.qt (optional)
missing module named 'qtpy.QtWebChannel' - imported by webview.platforms.qt (optional)
missing module named 'qtpy.QtNetwork' - imported by webview.platforms.qt (optional)
missing module named 'qtpy.QtGui' - imported by webview.platforms.qt (top-level)
missing module named 'qtpy.QtCore' - imported by webview.platforms.qt (top-level)
missing module named 'gi.repository' - imported by webview.platforms.gtk (top-level)
missing module named gi - imported by webview.platforms.gtk (top-level)
missing module named android - imported by webview.platforms.android (top-level)
missing module named jnius - imported by webview.platforms.android (top-level)
missing module named 'kivy.clock' - imported by webview.platforms.android (top-level)
missing module named 'kivy.uix' - imported by webview.platforms.android (top-level)
missing module named kivy - imported by webview.platforms.android (top-level)
missing module named chardet - imported by requests (optional)
