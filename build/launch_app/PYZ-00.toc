('/Users/<USER>/Desktop/AI Tutorials/storyboard/build/launch_app/PYZ-00.pyz',
 [('_compat_pickle',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_compression.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_threading_local.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/argparse.py',
   'PYMODULE'),
  ('base64',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/base64.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bisect.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/calendar.py',
   'PYMODULE'),
  ('configparser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/configparser.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/copy.py',
   'PYMODULE'),
  ('csv',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/csv.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/datetime.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/decimal.py',
   'PYMODULE'),
  ('email',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/utils.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fractions.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/getopt.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/gettext.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/gzip.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/hashlib.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/metadata.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/util.py',
   'PYMODULE'),
  ('logging',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/logging/__init__.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lzma.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/numbers.py',
   'PYMODULE'),
  ('optparse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/optparse.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pathlib.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pprint.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/py_compile.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/quopri.py',
   'PYMODULE'),
  ('random',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/random.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/selectors.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/shutil.py',
   'PYMODULE'),
  ('signal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/signal.py',
   'PYMODULE'),
  ('socket',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socket.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/statistics.py',
   'PYMODULE'),
  ('string',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/string.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/subprocess.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tarfile.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/threading.py',
   'PYMODULE'),
  ('token',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/token.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tracemalloc.py',
   'PYMODULE'),
  ('typing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/typing.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/parse.py',
   'PYMODULE'),
  ('uu',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/uu.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zipfile.py',
   'PYMODULE')])
