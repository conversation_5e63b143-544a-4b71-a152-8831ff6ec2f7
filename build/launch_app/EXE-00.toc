('/Users/<USER>/Desktop/AI Tutorials/storyboard/dist/StoryboardGenerator',
 <PERSON>als<PERSON>,
 <PERSON>als<PERSON>,
 <PERSON>als<PERSON>,
 None,
 None,
 <PERSON>alse,
 False,
 None,
 True,
 False,
 'arm64',
 None,
 None,
 '/Users/<USER>/Desktop/AI '
 'Tutorials/storyboard/build/launch_app/StoryboardGenerator.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/build/launch_app/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_struct.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/zlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/build/launch_app/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/build/launch_app/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/build/launch_app/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/build/launch_app/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('launch_app',
   '/Users/<USER>/Desktop/AI Tutorials/storyboard/launch_app.py',
   'PYSOURCE'),
  ('pyarrow/libarrow_substrait.2100.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_substrait.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_acero.2100.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_acero.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_parquet_encryption.2100.0.0.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_python_parquet_encryption.2100.0.0.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python.2100.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_python.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_parquet_encryption.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_python_parquet_encryption.dylib',
   'BINARY'),
  ('pyarrow/libarrow.2100.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_compute.2100.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_compute.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_dataset.2100.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_dataset.2100.dylib',
   'BINARY'),
  ('pyarrow/libparquet.2100.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libparquet.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_flight.2100.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_python_flight.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python.2100.0.0.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_python.2100.0.0.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_python.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_flight.2100.0.0.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_python_flight.2100.0.0.dylib',
   'BINARY'),
  ('pyarrow/libarrow_flight.2100.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_flight.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_parquet_encryption.2100.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_python_parquet_encryption.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_flight.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/libarrow_python_flight.dylib',
   'BINARY'),
  ('Python.framework/Versions/3.13/Python',
   '/Library/Frameworks/Python.framework/Versions/3.13/Python',
   'BINARY'),
  ('pyarrow/lib.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/lib.cpython-313-darwin.so',
   'BINARY'),
  ('lib-dynload/_csv.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_csv.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_statistics.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_contextvars.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_decimal.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_pickle.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_hashlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha3.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_blake2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_md5.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha1.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_random.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_bisect.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/math.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/array.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/select.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_socket.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/unicodedata.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_json.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/resource.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_lzma.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_bz2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/grp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/binascii.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_opcode.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_posixshmem.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_multiprocessing.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/pyexpat.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_scproxy.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/termios.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_ssl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/mmap.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_ctypes.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_queue.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_asyncio.cpython-313-darwin.so',
   'EXTENSION'),
  ('google/_upb/_message.abi3.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/google/_upb/_message.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_uuid.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_tests.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/_core/_multiarray_tests.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_umath.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/_core/_multiarray_umath.cpython-313-darwin.so',
   'EXTENSION'),
  ('charset_normalizer/md__mypyc.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/charset_normalizer/md__mypyc.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_multibytecodec.cpython-313-darwin.so',
   'EXTENSION'),
  ('charset_normalizer/md.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/charset_normalizer/md.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/readline.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/linalg/_umath_linalg.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/random/mtrand.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/random/bit_generator.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/random/_sfc64.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/random/_philox.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/random/_pcg64.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/random/_mt19937.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/random/_generator.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/random/_common.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/random/_bounded_integers.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_umath.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy/fft/_pocketfft_umath.cpython-313-darwin.so',
   'EXTENSION'),
  ('yaml/_yaml.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/yaml/_yaml.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/cmath.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/cmath.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/writers.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/writers.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_compute.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_compute.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_substrait.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_substrait.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_s3fs.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_s3fs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_parquet_encryption.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_parquet_encryption.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_parquet.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_parquet.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_orc.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_orc.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_json.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_json.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_hdfs.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_hdfs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_gcsfs.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_gcsfs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_fs.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_fs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_flight.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_flight.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset_parquet_encryption.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_dataset_parquet_encryption.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset_parquet.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_dataset_parquet.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset_orc.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_dataset_orc.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_dataset.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_csv.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_csv.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_azurefs.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_azurefs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_acero.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_acero.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_feather.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_feather.cpython-313-darwin.so',
   'EXTENSION'),
  ('markupsafe/_speedups.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/markupsafe/_speedups.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sqlite3.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sqlite3.cpython-313-darwin.so',
   'EXTENSION'),
  ('lxml/etree.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/etree.cpython-313-darwin.so',
   'EXTENSION'),
  ('lxml/_elementpath.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/_elementpath.cpython-313-darwin.so',
   'EXTENSION'),
  ('lxml/sax.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/sax.cpython-313-darwin.so',
   'EXTENSION'),
  ('lxml/objectify.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/objectify.cpython-313-darwin.so',
   'EXTENSION'),
  ('lxml/html/diff.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/html/diff.cpython-313-darwin.so',
   'EXTENSION'),
  ('lxml/html/_difflib.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/html/_difflib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lxml/builder.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/builder.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_elementtree.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/indexers.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/window/indexers.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/aggregations.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/window/aggregations.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/vectorized.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/vectorized.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/tzconversion.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/tzconversion.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timezones.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/timezones.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timestamps.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/timestamps.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timedeltas.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/timedeltas.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/strptime.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/strptime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/period.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/period.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/parsing.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/parsing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/offsets.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/offsets.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/np_datetime.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/np_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/nattype.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/nattype.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/fields.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/fields.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/dtypes.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/dtypes.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/conversion.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/conversion.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/ccalendar.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/ccalendar.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/base.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslibs/base.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslib.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/tslib.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/testing.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/testing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sparse.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/sparse.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sas.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/sas.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/reshape.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/reshape.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/properties.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/properties.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/parsers.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/parsers.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_parser.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/pandas_parser.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_datetime.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/pandas_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops_dispatch.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/ops_dispatch.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/ops.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/missing.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/missing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/lib.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/lib.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/json.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/json.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/join.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/join.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/interval.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/interval.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/internals.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/internals.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/indexing.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/indexing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/index.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/index.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashtable.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/hashtable.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashing.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/hashing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/groupby.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/groupby.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/byteswap.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/byteswap.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/arrays.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/arrays.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/algos.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/_libs/algos.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/_webp.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingtk.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/_imagingtk.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_avif.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/_avif.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/_imagingcms.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingmath.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/_imagingmath.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/_imaging.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_testcapi.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_testcapi.cpython-313-darwin.so',
   'EXTENSION'),
  ('tornado/speedups.abi3.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/tornado/speedups.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_curses.cpython-313-darwin.so',
   'EXTENSION'),
  ('rpds/rpds.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/rpds/rpds.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_heapq.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_jp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_kr.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_cn.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_tw.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_hk.cpython-313-darwin.so',
   'EXTENSION'),
  ('zstandard/_cffi.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/zstandard/_cffi.cpython-313-darwin.so',
   'EXTENSION'),
  ('zstandard/backend_c.cpython-313-darwin.so',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/zstandard/backend_c.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/fcntl.cpython-313-darwin.so',
   'EXTENSION'),
  ('libcrypto.3.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libssl.3.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/libssl.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebp.7.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/libwebp.7.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpmux.3.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/libwebpmux.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpdemux.2.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/libwebpdemux.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libavif.16.3.0.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/libavif.16.3.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblcms2.2.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/liblcms2.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libxcb.1.1.0.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/libxcb.1.1.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libjpeg.62.4.0.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/libjpeg.62.4.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libtiff.6.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/libtiff.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'BINARY'),
  ('PIL/.dylibs/libopenjp2.2.5.3.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/libopenjp2.2.5.3.dylib',
   'BINARY'),
  ('libncurses.6.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/libncurses.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libsharpyuv.0.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/libsharpyuv.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libXau.6.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/libXau.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblzma.5.dylib',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PIL/.dylibs/liblzma.5.dylib',
   'BINARY'),
  ('.env', '/Users/<USER>/Desktop/AI Tutorials/storyboard/.env', 'DATA'),
  ('.streamlit/config.toml',
   '/Users/<USER>/Desktop/AI Tutorials/storyboard/.streamlit/config.toml',
   'DATA'),
  ('main.py',
   '/Users/<USER>/Desktop/AI Tutorials/storyboard/main.py',
   'DATA'),
  ('storyboard.py',
   '/Users/<USER>/Desktop/AI Tutorials/storyboard/storyboard.py',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pyarrow/include/parquet/statistics.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/statistics.h',
   'DATA'),
  ('pyarrow/_fs.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_fs.pyx',
   'DATA'),
  ('pyarrow/include/arrow/util/binary_view_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/binary_view_util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/datetime.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/datetime.h',
   'DATA'),
  ('pyarrow/include/arrow/util/windows_compatibility.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/windows_compatibility.h',
   'DATA'),
  ('pyarrow/include/parquet/page_index.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/page_index.h',
   'DATA'),
  ('pyarrow/include/arrow/util/base64.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/base64.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/json.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/extension/json.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/expression.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/expression.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_run_end.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_run_end.h',
   'DATA'),
  ('pyarrow/include/arrow/type_traits.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/type_traits.h',
   'DATA'),
  ('pyarrow/include/arrow/python/inference.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/inference.h',
   'DATA'),
  ('pyarrow/tests/data/feather/v0.17.0.version.2-compression.lz4.feather',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/feather/v0.17.0.version.2-compression.lz4.feather',
   'DATA'),
  ('pyarrow/include/arrow/flight/transport_server.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/transport_server.h',
   'DATA'),
  ('pyarrow/include/arrow/chunk_resolver.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/chunk_resolver.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/portable-snippets/safe-math.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/portable-snippets/safe-math.h',
   'DATA'),
  ('pyarrow/include/arrow/util/future.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/future.h',
   'DATA'),
  ('pyarrow/src/arrow/python/flight.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/flight.cc',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h',
   'DATA'),
  ('pyarrow/includes/libarrow.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libarrow.pxd',
   'DATA'),
  ('pyarrow/include/arrow/buffer_builder.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/buffer_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_cookie_middleware.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_cookie_middleware.h',
   'DATA'),
  ('pyarrow/types.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/types.pxi',
   'DATA'),
  ('pyarrow/include/arrow/device.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/device.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_visit.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_visit.h',
   'DATA'),
  ('pyarrow/include/arrow/util/unreachable.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/unreachable.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/hdfs.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/hdfs.h',
   'DATA'),
  ('pyarrow/include/arrow/io/stdio.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/stdio.h',
   'DATA'),
  ('pyarrow/include/arrow/util/hashing.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/hashing.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/stl_allocator.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/stl_allocator.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/partition.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/partition.h',
   'DATA'),
  ('pyarrow/include/parquet/level_comparison.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/level_comparison.h',
   'DATA'),
  ('pyarrow/public-api.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/public-api.pxi',
   'DATA'),
  ('pyarrow/_orc.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_orc.pyx',
   'DATA'),
  ('pyarrow/benchmark.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/benchmark.pxi',
   'DATA'),
  ('pyarrow/include/parquet/encryption/crypto_factory.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/crypto_factory.h',
   'DATA'),
  ('pyarrow/include/arrow/python/benchmark.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/benchmark.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/key_encryption_key.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/key_encryption_key.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/azurefs.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/azurefs.h',
   'DATA'),
  ('pyarrow/include/parquet/parquet_version.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/parquet_version.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/api_aggregate.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api_aggregate.h',
   'DATA'),
  ('pyarrow/include/parquet/api/io.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/api/io.h',
   'DATA'),
  ('pyarrow/include/arrow/util/union_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/union_util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/secure_string.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/secure_string.h',
   'DATA'),
  ('pyarrow/include/arrow/status.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/status.h',
   'DATA'),
  ('pyarrow/include/parquet/api/reader.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/api/reader.h',
   'DATA'),
  ('pyarrow/include/parquet/metadata.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/metadata.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/util.h',
   'DATA'),
  ('pyarrow/includes/__init__.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/__init__.pxd',
   'DATA'),
  ('pyarrow/include/arrow/array/array_primitive.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_primitive.h',
   'DATA'),
  ('pyarrow/include/arrow/python/decimal.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/decimal.h',
   'DATA'),
  ('pyarrow/include/parquet/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/util/small_vector.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/small_vector.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/cached-powers.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/cached-powers.h',
   'DATA'),
  ('pyarrow/src/arrow/python/decimal.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/decimal.cc',
   'DATA'),
  ('pyarrow/include/arrow/result.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/result.h',
   'DATA'),
  ('pyarrow/include/arrow/util/converter.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/converter.h',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_interop.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_interop.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/mockfs.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/mockfs.h',
   'DATA'),
  ('pyarrow/include/arrow/python/lib.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/lib.h',
   'DATA'),
  ('pyarrow/tests/extensions.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/extensions.pyx',
   'DATA'),
  ('pyarrow/include/parquet/encryption/file_key_material_store.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/file_key_material_store.h',
   'DATA'),
  ('pyarrow/device.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/device.pxi',
   'DATA'),
  ('pyarrow/include/arrow/compute/util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/util.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_orc.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_orc.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bit_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/bit_util.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/executor_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/executor_util.h',
   'DATA'),
  ('pyarrow/includes/libarrow_fs.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libarrow_fs.pxd',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_csv.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_csv.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/test_nodes.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/test_nodes.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bit_block_counter.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/bit_block_counter.h',
   'DATA'),
  ('pyarrow/include/arrow/compare.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compare.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/benchmark_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/benchmark_util.h',
   'DATA'),
  ('pyarrow/_gcsfs.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_gcsfs.pyx',
   'DATA'),
  ('pyarrow/includes/libarrow_substrait.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libarrow_substrait.pxd',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_base.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_base.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server.h',
   'DATA'),
  ('pyarrow/include/arrow/tensor/converter.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/tensor/converter.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/engine/api.h',
   'DATA'),
  ('pyarrow/src/arrow/python/csv.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/csv.cc',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_convert.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_convert.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/path_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/path_util.h',
   'DATA'),
  ('pyarrow/include/arrow/io/test_common.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/test_common.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/api.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_time.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_time.h',
   'DATA'),
  ('pyarrow/include/arrow/adapters/orc/adapter.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/adapters/orc/adapter.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/utils.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/utils.h',
   'DATA'),
  ('pyarrow/include/arrow/util/compression.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/compression.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/local_wrap_kms_client.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/local_wrap_kms_client.h',
   'DATA'),
  ('pyarrow/include/arrow/python/io.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/io.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/encryption.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/encryption.h',
   'DATA'),
  ('pyarrow/src/arrow/python/helpers.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/helpers.h',
   'DATA'),
  ('pyarrow/includes/libparquet_encryption.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libparquet_encryption.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/float16.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/float16.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/query_context.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/query_context.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_auth.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_auth.h',
   'DATA'),
  ('pyarrow/src/arrow/python/filesystem.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/filesystem.cc',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_run_end.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_run_end.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow_api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow_api.h',
   'DATA'),
  ('pyarrow/_acero.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_acero.pxd',
   'DATA'),
  ('pyarrow/include/parquet/encryption/key_metadata.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/key_metadata.h',
   'DATA'),
  ('pyarrow/_azurefs.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_azurefs.pyx',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_internal.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_internal.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/visibility.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/visibility.h',
   'DATA'),
  ('pyarrow/include/parquet/file_reader.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/file_reader.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_init.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_init.h',
   'DATA'),
  ('pyarrow/include/arrow/c/dlpack_abi.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/c/dlpack_abi.h',
   'DATA'),
  ('pyarrow/include/arrow/util/span.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/span.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/writer.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/csv/writer.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/hash_join_node.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/hash_join_node.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_dict.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_dict.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_ops.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_ops.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/hash_join.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/hash_join.h',
   'DATA'),
  ('pyarrow/_json.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_json.pyx',
   'DATA'),
  ('pyarrow/include/arrow/acero/task_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/task_util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/python_to_arrow.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/python_to_arrow.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_writer.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/util/test_common.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/test_common.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_json.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_json.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/reader.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/reader.h',
   'DATA'),
  ('pyarrow/include/parquet/platform.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/platform.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/visibility.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/mutex.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/mutex.h',
   'DATA'),
  ('pyarrow/include/arrow/python/iterators.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/iterators.h',
   'DATA'),
  ('pyarrow/include/arrow/python/pyarrow_lib.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/pyarrow_lib.h',
   'DATA'),
  ('pyarrow/include/arrow/io/transform.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/transform.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_init.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_init.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/key_value_metadata.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/key_value_metadata.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/xxhash/xxhash.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/xxhash/xxhash.h',
   'DATA'),
  ('pyarrow/tests/data/parquet/v0.7.1.some-named-index.parquet',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/parquet/v0.7.1.some-named-index.parquet',
   'DATA'),
  ('pyarrow/include/arrow/pretty_print.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/pretty_print.h',
   'DATA'),
  ('pyarrow/_parquet_encryption.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_parquet_encryption.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/api.h',
   'DATA'),
  ('pyarrow/tests/data/orc/decimal.jsn.gz',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/orc/decimal.jsn.gz',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/filesystem.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/filesystem.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/test_plan_builder.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/test_plan_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/diy-fp.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/diy-fp.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/transport.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/transport.h',
   'DATA'),
  ('pyarrow/include/arrow/util/delimiting.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/delimiting.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/bool8.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/extension/bool8.h',
   'DATA'),
  ('pyarrow/include/arrow/util/queue.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/queue.h',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_to_arrow.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_to_arrow.h',
   'DATA'),
  ('pyarrow/_parquet.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_parquet.pxd',
   'DATA'),
  ('pyarrow/include/arrow/acero/exec_plan.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/exec_plan.h',
   'DATA'),
  ('pyarrow/include/arrow/util/async_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/async_util.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/localfs.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/localfs.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/bignum.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/bignum.h',
   'DATA'),
  ('pyarrow/include/arrow/util/simd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/simd.h',
   'DATA'),
  ('pyarrow/include/arrow/record_batch.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/record_batch.h',
   'DATA'),
  ('pyarrow/memory.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/memory.pxi',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/string-to-double.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/string-to-double.h',
   'DATA'),
  ('pyarrow/include/arrow/python/flight.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/flight.h',
   'DATA'),
  ('pyarrow/include/arrow/python/lib_api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/lib_api.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/serde.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/serde.h',
   'DATA'),
  ('pyarrow/tests/pyarrow_cython_example.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/pyarrow_cython_example.pyx',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/relation.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/relation.h',
   'DATA'),
  ('pyarrow/include/parquet/types.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/types.h',
   'DATA'),
  ('pyarrow/src/arrow/python/gdb.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/gdb.h',
   'DATA'),
  ('pyarrow/include/arrow/json/from_string.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/from_string.h',
   'DATA'),
  ('pyarrow/include/parquet/windows_compatibility.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/windows_compatibility.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/accumulation_queue.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/accumulation_queue.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/tz_private.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/tz_private.h',
   'DATA'),
  ('pyarrow/include/arrow/util/iterator.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/iterator.h',
   'DATA'),
  ('pyarrow/include/arrow/util/windows_fixup.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/windows_fixup.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/gtest_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/gtest_util.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/test_common.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/csv/test_common.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/xxhash.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/xxhash.h',
   'DATA'),
  ('pyarrow/include/arrow/util/int_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/int_util.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/parquet_encryption_config.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/parquet_encryption_config.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/type_fwd.h',
   'DATA'),
  ('pyarrow/include/parquet/schema.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/schema.h',
   'DATA'),
  ('pyarrow/include/arrow/util/thread_pool.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/thread_pool.h',
   'DATA'),
  ('pyarrow/_dataset_parquet.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_dataset_parquet.pyx',
   'DATA'),
  ('pyarrow/include/arrow/csv/options.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/csv/options.h',
   'DATA'),
  ('pyarrow/include/arrow/c/bridge.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/c/bridge.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/matchers.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/matchers.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/fixed_width_test_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/fixed_width_test_util.h',
   'DATA'),
  ('pyarrow/_csv.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_csv.pxd',
   'DATA'),
  ('pyarrow/include/parquet/column_scanner.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/column_scanner.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_builders.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_builders.h',
   'DATA'),
  ('pyarrow/include/parquet/stream_reader.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/stream_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/feather.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/feather.h',
   'DATA'),
  ('pyarrow/include/arrow/python/common.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/common.h',
   'DATA'),
  ('pyarrow/include/arrow/io/concurrency.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/concurrency.h',
   'DATA'),
  ('pyarrow/scalar.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/scalar.pxi',
   'DATA'),
  ('pyarrow/include/arrow/json/converter.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/converter.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_to_arrow.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_to_arrow.cc',
   'DATA'),
  ('pyarrow/include/arrow/acero/order_by_impl.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/order_by_impl.h',
   'DATA'),
  ('pyarrow/include/arrow/util/concurrent_map.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/concurrent_map.h',
   'DATA'),
  ('pyarrow/tests/data/parquet/v0.7.1.all-named-index.parquet',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/parquet/v0.7.1.all-named-index.parquet',
   'DATA'),
  ('pyarrow/include/arrow/dataset/plan.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/plan.h',
   'DATA'),
  ('pyarrow/include/arrow/util/compare.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/compare.h',
   'DATA'),
  ('pyarrow/include/arrow/python/pyarrow_api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/pyarrow_api.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_nested.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_nested.h',
   'DATA'),
  ('pyarrow/include/arrow/util/aligned_storage.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/aligned_storage.h',
   'DATA'),
  ('pyarrow/include/parquet/hasher.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/hasher.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/registry.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/registry.h',
   'DATA'),
  ('pyarrow/src/arrow/python/filesystem.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/filesystem.h',
   'DATA'),
  ('pyarrow/_hdfs.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_hdfs.pyx',
   'DATA'),
  ('pyarrow/include/arrow/json/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/api.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/ieee.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/ieee.h',
   'DATA'),
  ('pyarrow/include/arrow/json/chunker.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/chunker.h',
   'DATA'),
  ('pyarrow/include/arrow/array/concatenate.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/concatenate.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bit_run_reader.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/bit_run_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_definitions.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_definitions.h',
   'DATA'),
  ('pyarrow/include/arrow/python/parquet_encryption.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/parquet_encryption.h',
   'DATA'),
  ('pyarrow/include/arrow/config.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/config.h',
   'DATA'),
  ('pyarrow/include/parquet/bloom_filter_reader.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/bloom_filter_reader.h',
   'DATA'),
  ('pyarrow/io.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/io.pxi',
   'DATA'),
  ('pyarrow/tests/bound_function_visit_strings.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/bound_function_visit_strings.pyx',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.test1.jsn.gz',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.test1.jsn.gz',
   'DATA'),
  ('pyarrow/_compute.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_compute.pyx',
   'DATA'),
  ('pyarrow/include/arrow/util/math_constants.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/math_constants.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/exec.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/exec.h',
   'DATA'),
  ('pyarrow/include/arrow/util/endian.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/endian.h',
   'DATA'),
  ('pyarrow/include/arrow/util/prefetch.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/prefetch.h',
   'DATA'),
  ('pyarrow/include/arrow/io/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/api.h',
   'DATA'),
  ('pyarrow/include/arrow/json/chunked_builder.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/chunked_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/schema_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/schema_util.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/aggregate_node.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/aggregate_node.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/api_vector.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api_vector.h',
   'DATA'),
  ('pyarrow/include/arrow/python/platform.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/platform.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/gtest_compat.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/gtest_compat.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/key_toolkit.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/key_toolkit.h',
   'DATA'),
  ('pyarrow/include/parquet/properties.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/properties.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_adaptive.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_adaptive.h',
   'DATA'),
  ('pyarrow/include/arrow/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/type_fwd.h',
   'DATA'),
  ('pyarrow/lib.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/lib.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/two_level_cache_with_expiration.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/two_level_cache_with_expiration.h',
   'DATA'),
  ('pyarrow/include/arrow/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/api.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_test.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_test.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/file_key_wrapper.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/file_key_wrapper.h',
   'DATA'),
  ('pyarrow/_dataset_orc.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_dataset_orc.pyx',
   'DATA'),
  ('pyarrow/gandiva.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/gandiva.pyx',
   'DATA'),
  ('pyarrow/src/arrow/python/common.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/common.cc',
   'DATA'),
  ('pyarrow/include/arrow/vendored/ProducerConsumerQueue.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/ProducerConsumerQueue.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/backpressure_handler.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/backpressure_handler.h',
   'DATA'),
  ('pyarrow/include/parquet/exception.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/exception.h',
   'DATA'),
  ('pyarrow/include/arrow/python/gdb.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/gdb.h',
   'DATA'),
  ('pyarrow/include/arrow/python/udf.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/udf.h',
   'DATA'),
  ('pyarrow/_cuda.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_cuda.pxd',
   'DATA'),
  ('pyarrow/include/arrow/compute/row/grouper.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/row/grouper.h',
   'DATA'),
  ('pyarrow/include/arrow/sparse_tensor.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/sparse_tensor.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/file_key_unwrapper.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/file_key_unwrapper.h',
   'DATA'),
  ('pyarrow/lib_api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/lib_api.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/uuid.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/extension/uuid.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_decimal.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_decimal.h',
   'DATA'),
  ('pyarrow/include/parquet/level_conversion.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/level_conversion.h',
   'DATA'),
  ('pyarrow/includes/libgandiva.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libgandiva.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/datetime.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/datetime.cc',
   'DATA'),
  ('pyarrow/include/arrow/extension/opaque.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/extension/opaque.h',
   'DATA'),
  ('pyarrow/include/parquet/api/schema.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/api/schema.h',
   'DATA'),
  ('pyarrow/include/arrow/util/utf8.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/utf8.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/benchmark_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/benchmark_util.h',
   'DATA'),
  ('pyarrow/_fs.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_fs.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/platform.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/platform.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api.h',
   'DATA'),
  ('pyarrow/include/arrow/visitor.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/visitor.h',
   'DATA'),
  ('pyarrow/include/arrow/type.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/type.h',
   'DATA'),
  ('pyarrow/include/arrow/util/async_generator.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/async_generator.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_union.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_union.h',
   'DATA'),
  ('pyarrow/lib.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/lib.pyx',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_middleware.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/util/ree_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/ree_util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/ubsan.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/ubsan.h',
   'DATA'),
  ('pyarrow/includes/libarrow_dataset_parquet.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libarrow_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/udf.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/udf.cc',
   'DATA'),
  ('pyarrow/src/arrow/python/python_to_arrow.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_to_arrow.h',
   'DATA'),
  ('pyarrow/_feather.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_feather.pyx',
   'DATA'),
  ('pyarrow/src/arrow/python/decimal.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/decimal.h',
   'DATA'),
  ('pyarrow/include/parquet/geospatial/statistics.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/geospatial/statistics.h',
   'DATA'),
  ('pyarrow/include/arrow/json/reader.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/reader.h',
   'DATA'),
  ('pyarrow/src/arrow/python/benchmark.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/benchmark.cc',
   'DATA'),
  ('pyarrow/_dataset.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_dataset.pxd',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_tracing_middleware.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_tracing_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/util/cpu_info.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/cpu_info.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/generator.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/generator.h',
   'DATA'),
  ('pyarrow/include/arrow/util/logger.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/logger.h',
   'DATA'),
  ('pyarrow/include/arrow/util/pcg_random.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/pcg_random.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_base.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_base.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/visibility.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp',
   'DATA'),
  ('pyarrow/builder.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/builder.pxi',
   'DATA'),
  ('pyarrow/tests/data/orc/README.md',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/orc/README.md',
   'DATA'),
  ('pyarrow/include/arrow/dataset/projector.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/projector.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/gcsfs.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/gcsfs.h',
   'DATA'),
  ('pyarrow/include/arrow/json/parser.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/parser.h',
   'DATA'),
  ('pyarrow/include/arrow/util/debug.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/debug.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/kms_client_factory.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/kms_client_factory.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_binary.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_binary.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/dictionary.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/dictionary.h',
   'DATA'),
  ('pyarrow/includes/libarrow_dataset.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libarrow_dataset.pxd',
   'DATA'),
  ('pyarrow/include/arrow/ipc/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/util/hash_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/hash_util.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h',
   'DATA'),
  ('pyarrow/_compute.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_compute.pxd',
   'DATA'),
  ('pyarrow/include/arrow/acero/map_node.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/map_node.h',
   'DATA'),
  ('pyarrow/include/arrow/io/interfaces.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/interfaces.h',
   'DATA'),
  ('pyarrow/includes/libarrow_feather.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libarrow_feather.pxd',
   'DATA'),
  ('pyarrow/include/arrow/datum.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/datum.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_ipc.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_ipc.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/scanner.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/scanner.h',
   'DATA'),
  ('pyarrow/include/arrow/array/data.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/data.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/bloom_filter.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/bloom_filter.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/parser.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/csv/parser.h',
   'DATA'),
  ('pyarrow/include/arrow/tensor.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/tensor.h',
   'DATA'),
  ('pyarrow/includes/common.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/common.pxd',
   'DATA'),
  ('pyarrow/include/parquet/encryption/kms_client.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/kms_client.h',
   'DATA'),
  ('pyarrow/src/arrow/python/arrow_to_pandas.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/arrow_to_pandas.cc',
   'DATA'),
  ('pyarrow/include/arrow/python/helpers.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/helpers.h',
   'DATA'),
  ('pyarrow/src/arrow/python/ipc.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/ipc.h',
   'DATA'),
  ('pyarrow/include/arrow/util/functional.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/functional.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_auth_handlers.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_auth_handlers.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_to_arrow.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_to_arrow.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_dict.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_dict.h',
   'DATA'),
  ('pyarrow/tests/data/parquet/v0.7.1.parquet',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/parquet/v0.7.1.parquet',
   'DATA'),
  ('pyarrow/include/arrow/dataset/dataset.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/dataset.h',
   'DATA'),
  ('pyarrow/include/arrow/io/mman.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/mman.h',
   'DATA'),
  ('pyarrow/src/arrow/python/extension_type.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/extension_type.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.testDate1900.jsn.gz',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.testDate1900.jsn.gz',
   'DATA'),
  ('pyarrow/src/arrow/python/visibility.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/byte_size.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/byte_size.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/visibility.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/cancel.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/cancel.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/tz.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/tz.h',
   'DATA'),
  ('pyarrow/include/arrow/util/logging.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/logging.h',
   'DATA'),
  ('pyarrow/__init__.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/__init__.pxd',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/options.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/options.h',
   'DATA'),
  ('pyarrow/src/arrow/python/extension_type.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/extension_type.cc',
   'DATA'),
  ('pyarrow/include/arrow/ipc/util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/util.h',
   'DATA'),
  ('pyarrow/include/parquet/printer.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/printer.h',
   'DATA'),
  ('pyarrow/include/arrow/adapters/tensorflow/convert.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/adapters/tensorflow/convert.h',
   'DATA'),
  ('pyarrow/include/parquet/size_statistics.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/size_statistics.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/message.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/message.h',
   'DATA'),
  ('pyarrow/include/arrow/util/vector.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/vector.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/util.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/types_async.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/types_async.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h',
   'DATA'),
  ('pyarrow/include/arrow/util/formatting.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/formatting.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/visibility.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/visibility.h',
   'DATA'),
  ('pyarrow/_flight.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_flight.pyx',
   'DATA'),
  ('pyarrow/src/arrow/python/inference.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/inference.h',
   'DATA'),
  ('pyarrow/include/arrow/visit_data_inline.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/visit_data_inline.h',
   'DATA'),
  ('pyarrow/include/parquet/column_reader.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/column_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_nested.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_nested.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server_auth.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server_auth.h',
   'DATA'),
  ('pyarrow/include/arrow/memory_pool.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/memory_pool.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.emptyFile.orc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.emptyFile.orc',
   'DATA'),
  ('pyarrow/include/arrow/table.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/table.h',
   'DATA'),
  ('pyarrow/include/arrow/python/extension_type.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/extension_type.h',
   'DATA'),
  ('pyarrow/_pyarrow_cpp_tests.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_pyarrow_cpp_tests.pxd',
   'DATA'),
  ('pyarrow/include/arrow/acero/partition_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/partition_util.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/function.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/function.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_decimal.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow_lib.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow_lib.h',
   'DATA'),
  ('pyarrow/includes/libarrow_cuda.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libarrow_cuda.pxd',
   'DATA'),
  ('pyarrow/include/arrow/c/helpers.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/c/helpers.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/ios.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/ios.h',
   'DATA'),
  ('pyarrow/src/arrow/python/udf.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/udf.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/tpch_node.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/tpch_node.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_generate.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_generate.h',
   'DATA'),
  ('pyarrow/includes/libparquet.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libparquet.pxd',
   'DATA'),
  ('pyarrow/include/parquet/level_conversion_inc.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/level_conversion_inc.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/future_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/future_util.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/api_scalar.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api_scalar.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/buffer.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/buffer.h',
   'DATA'),
  ('pyarrow/include/parquet/arrow/reader.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/arrow/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/test_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/test_util.h',
   'DATA'),
  ('pyarrow/_substrait.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_substrait.pyx',
   'DATA'),
  ('pyarrow/include/arrow/python/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/api.h',
   'DATA'),
  ('pyarrow/include/arrow/python/async.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/async.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/invalid_row.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/csv/invalid_row.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/s3fs.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/s3fs.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_convert.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_convert.cc',
   'DATA'),
  ('pyarrow/include/arrow/compute/kernel.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/kernel.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/chunker.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/csv/chunker.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/random.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/random.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/options.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/options.h',
   'DATA'),
  ('pyarrow/src/arrow/python/csv.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/csv.h',
   'DATA'),
  ('pyarrow/include/parquet/arrow/schema.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/arrow/schema.h',
   'DATA'),
  ('pyarrow/include/arrow/util/time.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/time.h',
   'DATA'),
  ('pyarrow/include/arrow/util/basic_decimal.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/basic_decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/api.h',
   'DATA'),
  ('pyarrow/include/arrow/util/string.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/string.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_base.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_base.h',
   'DATA'),
  ('pyarrow/include/arrow/scalar.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/scalar.h',
   'DATA'),
  ('pyarrow/src/arrow/python/CMakeLists.txt',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/CMakeLists.txt',
   'DATA'),
  ('pyarrow/include/arrow/json/test_common.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/test_common.h',
   'DATA'),
  ('pyarrow/includes/libarrow_flight.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libarrow_flight.pxd',
   'DATA'),
  ('pyarrow/tests/data/parquet/v0.7.1.column-metadata-handling.parquet',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/parquet/v0.7.1.column-metadata-handling.parquet',
   'DATA'),
  ('pyarrow/include/parquet/stream_writer.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/stream_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/date.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/date.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/double-conversion.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/double-conversion.h',
   'DATA'),
  ('pyarrow/_parquet_encryption.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow/include/arrow/testing/uniform_real.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/uniform_real.h',
   'DATA'),
  ('pyarrow/include/arrow/array.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array.h',
   'DATA'),
  ('pyarrow/include/arrow/util/tracing.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/tracing.h',
   'DATA'),
  ('pyarrow/include/parquet/file_writer.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/file_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_flight_server.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_flight_server.h',
   'DATA'),
  ('pyarrow/include/arrow/python/arrow_to_pandas.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/arrow_to_pandas.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/math.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/math.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/type_fwd.h',
   'DATA'),
  ('pyarrow/_dataset_parquet_encryption.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_dataset_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow/include/arrow/flight/otel_logging.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/otel_logging.h',
   'DATA'),
  ('pyarrow/include/arrow/json/object_writer.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/object_writer.h',
   'DATA'),
  ('pyarrow/includes/libarrow_python.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libarrow_python.pxd',
   'DATA'),
  ('pyarrow/tests/data/orc/decimal.orc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/orc/decimal.orc',
   'DATA'),
  ('pyarrow/include/arrow/util/checked_cast.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/checked_cast.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_parquet.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_parquet.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/api.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/filesystem_library.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/filesystem_library.h',
   'DATA'),
  ('pyarrow/src/arrow/python/gdb.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/gdb.cc',
   'DATA'),
  ('pyarrow/include/parquet/benchmark_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/benchmark_util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/helpers.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/helpers.cc',
   'DATA'),
  ('pyarrow/_csv.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_csv.pyx',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_init.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_init.h',
   'DATA'),
  ('pyarrow/include/arrow/builder.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/builder.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow.cc',
   'DATA'),
  ('pyarrow/include/arrow/array/array_binary.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_binary.h',
   'DATA'),
  ('pyarrow/include/arrow/util/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/io/hdfs.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/hdfs.h',
   'DATA'),
  ('pyarrow/include/parquet/column_writer.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/column_writer.h',
   'DATA'),
  ('pyarrow/lib.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/lib.pxd',
   'DATA'),
  ('pyarrow/include/arrow/c/abi.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/c/abi.h',
   'DATA'),
  ('pyarrow/_s3fs.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_s3fs.pyx',
   'DATA'),
  ('pyarrow/_cuda.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_cuda.pyx',
   'DATA'),
  ('pyarrow/src/arrow/python/async.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/async.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/column_decoder.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/csv/column_decoder.h',
   'DATA'),
  ('pyarrow/include/arrow/util/io_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/io_util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.testDate1900.orc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.testDate1900.orc',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/test_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/platform.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/platform.h',
   'DATA'),
  ('pyarrow/include/arrow/io/file.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/file.h',
   'DATA'),
  ('pyarrow/include/arrow/array/statistics.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/statistics.h',
   'DATA'),
  ('pyarrow/array.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/array.pxi',
   'DATA'),
  ('pyarrow/src/arrow/python/python_to_arrow.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_to_arrow.cc',
   'DATA'),
  ('pyarrow/src/arrow/python/benchmark.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/benchmark.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/double-to-string.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/double-to-string.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/arrow_to_python_internal.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/arrow_to_python_internal.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server_tracing_middleware.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server_tracing_middleware.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.test1.orc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.test1.orc',
   'DATA'),
  ('pyarrow/_pyarrow_cpp_tests.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_pyarrow_cpp_tests.pyx',
   'DATA'),
  ('pyarrow/include/arrow/util/range.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/range.h',
   'DATA'),
  ('pyarrow/src/arrow/python/ipc.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/ipc.cc',
   'DATA'),
  ('pyarrow/include/arrow/stl_iterator.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/stl_iterator.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/async_test_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/async_test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/fixed_shape_tensor.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/extension/fixed_shape_tensor.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/api.h',
   'DATA'),
  ('pyarrow/include/arrow/device_allocation_type_set.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/device_allocation_type_set.h',
   'DATA'),
  ('pyarrow/include/arrow/io/memory.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/memory.h',
   'DATA'),
  ('pyarrow/include/arrow/io/slow.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/slow.h',
   'DATA'),
  ('pyarrow/_acero.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_acero.pyx',
   'DATA'),
  ('pyarrow/include/arrow/vendored/pcg/pcg_random.hpp',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_random.hpp',
   'DATA'),
  ('pyarrow/include/arrow/acero/options.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/options.h',
   'DATA'),
  ('pyarrow/src/arrow/python/parquet_encryption.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/parquet_encryption.h',
   'DATA'),
  ('pyarrow/include/arrow/stl.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/stl.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/strtod.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/strtod.h',
   'DATA'),
  ('pyarrow/src/arrow/python/io.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/io.cc',
   'DATA'),
  ('pyarrow/include/arrow/python/python_test.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/python_test.h',
   'DATA'),
  ('pyarrow/include/parquet/arrow/writer.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/arrow/writer.h',
   'DATA'),
  ('pyarrow/_dataset_parquet.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow/include/arrow/flight/middleware.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/middleware.h',
   'DATA'),
  ('pyarrow/src/arrow/python/parquet_encryption.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/parquet_encryption.cc',
   'DATA'),
  ('pyarrow/include/arrow/acero/time_series_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/time_series_util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/crc32.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/crc32.h',
   'DATA'),
  ('pyarrow/pandas-shim.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/pandas-shim.pxi',
   'DATA'),
  ('pyarrow/include/arrow/visit_scalar_inline.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/visit_scalar_inline.h',
   'DATA'),
  ('pyarrow/ipc.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/ipc.pxi',
   'DATA'),
  ('pyarrow/src/arrow/python/inference.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/inference.cc',
   'DATA'),
  ('pyarrow/include/arrow/dataset/dataset_writer.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/dataset_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/util/algorithm.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/algorithm.h',
   'DATA'),
  ('pyarrow/include/arrow/adapters/orc/options.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/adapters/orc/options.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/builder.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/builder.h',
   'DATA'),
  ('pyarrow/include/arrow/io/compressed.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/compressed.h',
   'DATA'),
  ('pyarrow/include/arrow/io/caching.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/caching.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/test_encryption_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/test_encryption_util.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/discovery.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/discovery.h',
   'DATA'),
  ('pyarrow/include/arrow/json/options.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/options.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/api.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/function_options.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/function_options.h',
   'DATA'),
  ('pyarrow/include/arrow/io/buffered.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/buffered.h',
   'DATA'),
  ('pyarrow/include/arrow/util/decimal.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/decimal.h',
   'DATA'),
  ('pyarrow/include/parquet/bloom_filter.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/bloom_filter.h',
   'DATA'),
  ('pyarrow/include/parquet/level_comparison_inc.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/level_comparison_inc.h',
   'DATA'),
  ('pyarrow/src/arrow/python/common.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/common.h',
   'DATA'),
  ('pyarrow/include/arrow/json/rapidjson_defs.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/rapidjson_defs.h',
   'DATA'),
  ('pyarrow/include/arrow/util/regex.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/regex.h',
   'DATA'),
  ('pyarrow/include/parquet/arrow/test_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/arrow/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/api.h',
   'DATA'),
  ('pyarrow/include/arrow/python/vendored/pythoncapi_compat.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/vendored/pythoncapi_compat.h',
   'DATA'),
  ('pyarrow/include/arrow/visitor_generate.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/visitor_generate.h',
   'DATA'),
  ('pyarrow/include/arrow/python/type_traits.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/type_traits.h',
   'DATA'),
  ('pyarrow/include/arrow/array/util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/util.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/converter.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/csv/converter.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server_middleware.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/memory_pool_test.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/memory_pool_test.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_reader.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/asof_join_node.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/asof_join_node.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/file_system_key_material_store.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/file_system_key_material_store.h',
   'DATA'),
  ('pyarrow/src/arrow/python/iterators.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/iterators.h',
   'DATA'),
  ('pyarrow/include/arrow/c/dlpack.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/c/dlpack.h',
   'DATA'),
  ('pyarrow/include/arrow/io/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/io/type_fwd.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_interop.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_interop.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/test_in_memory_kms.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/test_in_memory_kms.h',
   'DATA'),
  ('pyarrow/include/arrow/util/task_group.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/task_group.h',
   'DATA'),
  ('pyarrow/error.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/error.pxi',
   'DATA'),
  ('pyarrow/include/arrow/python/ipc.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/ipc.h',
   'DATA'),
  ('pyarrow/include/parquet/encoding.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encoding.h',
   'DATA'),
  ('pyarrow/include/arrow/util/parallel.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/parallel.h',
   'DATA'),
  ('pyarrow/include/arrow/table_builder.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/table_builder.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/key_material.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/key_material.h',
   'DATA'),
  ('pyarrow/include/parquet/windows_fixup.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/windows_fixup.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/test_common.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/test_common.h',
   'DATA'),
  ('pyarrow/include/parquet/test_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/list_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/list_util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/util.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/hash_join_dict.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/acero/hash_join_dict.h',
   'DATA'),
  ('pyarrow/include/arrow/visit_array_inline.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/visit_array_inline.h',
   'DATA'),
  ('pyarrow/include/arrow/python/csv.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/csv.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/initialize.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/initialize.h',
   'DATA'),
  ('pyarrow/src/arrow/python/arrow_to_pandas.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/arrow_to_pandas.h',
   'DATA'),
  ('pyarrow/include/arrow/json/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/util/type_traits.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/type_traits.h',
   'DATA'),
  ('pyarrow/include/arrow/array/validate.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/validate.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/cast.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/cast.h',
   'DATA'),
  ('pyarrow/include/arrow/extension_type.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/extension_type.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_convert.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_convert.h',
   'DATA'),
  ('pyarrow/src/arrow/python/vendored/pythoncapi_compat.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/vendored/pythoncapi_compat.h',
   'DATA'),
  ('pyarrow/include/arrow/chunked_array.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/chunked_array.h',
   'DATA'),
  ('pyarrow/include/arrow/util/align_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/align_util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/visibility.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/visibility.h',
   'DATA'),
  ('pyarrow/src/arrow/python/util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/launder.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/launder.h',
   'DATA'),
  ('pyarrow/src/arrow/python/flight.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/flight.h',
   'DATA'),
  ('pyarrow/_dataset.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_dataset.pyx',
   'DATA'),
  ('pyarrow/include/arrow/vendored/strptime.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/strptime.h',
   'DATA'),
  ('pyarrow/src/arrow/python/util.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/util.cc',
   'DATA'),
  ('pyarrow/include/arrow/visit_type_inline.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/visit_type_inline.h',
   'DATA'),
  ('pyarrow/include/arrow/util/rows_to_batches.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/rows_to_batches.h',
   'DATA'),
  ('pyarrow/include/arrow/util/macros.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/macros.h',
   'DATA'),
  ('pyarrow/include/parquet/xxhasher.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/xxhasher.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/reader.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/csv/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/json/object_parser.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/json/object_parser.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_primitive.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_primitive.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/extension_type.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/extension_type.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/process.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/testing/process.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.emptyFile.jsn.gz',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.emptyFile.jsn.gz',
   'DATA'),
  ('pyarrow/src/arrow/python/datetime.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/datetime.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/api.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/csv/api.h',
   'DATA'),
  ('pyarrow/table.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/table.pxi',
   'DATA'),
  ('pyarrow/include/parquet/column_page.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/column_page.h',
   'DATA'),
  ('pyarrow/include/arrow/util/config.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/config.h',
   'DATA'),
  ('pyarrow/_parquet.pyx',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_parquet.pyx',
   'DATA'),
  ('pyarrow/include/arrow/util/async_generator_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/async_generator_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/util/int_util_overflow.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/int_util_overflow.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/visibility.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/visibility.h',
   'DATA'),
  ('pyarrow/src/arrow/python/type_traits.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/type_traits.h',
   'DATA'),
  ('pyarrow/_orc.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_orc.pxd',
   'DATA'),
  ('pyarrow/include/arrow/csv/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/csv/type_fwd.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_test.cc',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_test.cc',
   'DATA'),
  ('pyarrow/include/parquet/api/writer.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/parquet/api/writer.h',
   'DATA'),
  ('pyarrow/include/arrow/util/visibility.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/array/diff.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/array/diff.h',
   'DATA'),
  ('pyarrow/compat.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/compat.pxi',
   'DATA'),
  ('pyarrow/config.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/config.pxi',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/s3_test_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/s3_test_util.h',
   'DATA'),
  ('pyarrow/_dlpack.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_dlpack.pxi',
   'DATA'),
  ('pyarrow/include/arrow/csv/column_builder.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/csv/column_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/python/filesystem.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/filesystem.h',
   'DATA'),
  ('pyarrow/_json.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/_json.pxd',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/extension_set.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/extension_set.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/writer.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/writer.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/type_fwd.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/util/string_util.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/string_util.h',
   'DATA'),
  ('pyarrow/includes/libarrow_acero.pxd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/includes/libarrow_acero.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/vendored/CMakeLists.txt',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/vendored/CMakeLists.txt',
   'DATA'),
  ('pyarrow/include/arrow/util/value_parsing.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/value_parsing.h',
   'DATA'),
  ('pyarrow/src/arrow/python/io.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/src/arrow/python/io.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/types.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/flight/types.h',
   'DATA'),
  ('pyarrow/include/arrow/python/pyarrow.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/python/pyarrow.h',
   'DATA'),
  ('pyarrow/include/arrow/util/uri.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/util/uri.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/visibility.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/visibility.h',
   'DATA'),
  ('pyarrow/tensor.pxi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/tensor.pxi',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/extension_types.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/extension_types.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/ordering.h',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pyarrow/include/arrow/compute/ordering.h',
   'DATA'),
  ('pandas/io/formats/templates/string.tpl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/io/formats/templates/string.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_style.tpl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/io/formats/templates/html_style.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_longtable.tpl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/io/formats/templates/latex_longtable.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_table.tpl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/io/formats/templates/latex_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html.tpl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/io/formats/templates/html.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_table.tpl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/io/formats/templates/html_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex.tpl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pandas/io/formats/templates/latex.tpl',
   'DATA'),
  ('pytz/zoneinfo/America/Guyana',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Guyana',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jayapura',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jayapura',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kolkata',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kolkata',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuala_Lumpur',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuala_Lumpur',
   'DATA'),
  ('pytz/zoneinfo/America/Winnipeg',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Winnipeg',
   'DATA'),
  ('pytz/zoneinfo/Europe/Amsterdam',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Amsterdam',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tashkent',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tashkent',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ust-Nera',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ust-Nera',
   'DATA'),
  ('pytz/zoneinfo/America/Los_Angeles',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Los_Angeles',
   'DATA'),
  ('pytz/zoneinfo/America/Boise',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Boise',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bangkok',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bangkok',
   'DATA'),
  ('pytz/zoneinfo/US/Arizona',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/US/Arizona',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novosibirsk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Novosibirsk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Warsaw',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Warsaw',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT0',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT0',
   'DATA'),
  ('pytz/zoneinfo/America/Guadeloupe',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Guadeloupe',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-11',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-11',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   'DATA'),
  ('pytz/zoneinfo/America/Tijuana',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Tijuana',
   'DATA'),
  ('pytz/zoneinfo/America/Nome',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Nome',
   'DATA'),
  ('pytz/zoneinfo/GB',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/GB',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lindeman',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Lindeman',
   'DATA'),
  ('pytz/zoneinfo/Israel',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Israel',
   'DATA'),
  ('pytz/zoneinfo/MET',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/MET',
   'DATA'),
  ('pytz/zoneinfo/America/Grand_Turk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Grand_Turk',
   'DATA'),
  ('pytz/zoneinfo/Chile/EasterIsland',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Chile/EasterIsland',
   'DATA'),
  ('pytz/zoneinfo/America/Blanc-Sablon',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Blanc-Sablon',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaSur',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/BajaSur',
   'DATA'),
  ('pytz/zoneinfo/Australia/Eucla',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Eucla',
   'DATA'),
  ('pytz/zoneinfo/Australia/North',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/North',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dhaka',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dhaka',
   'DATA'),
  ('pytz/zoneinfo/America/Monterrey',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Monterrey',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belgrade',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Belgrade',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Luis',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/San_Luis',
   'DATA'),
  ('pytz/zoneinfo/Europe/Brussels',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Brussels',
   'DATA'),
  ('pytz/zoneinfo/tzdata.zi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/tzdata.zi',
   'DATA'),
  ('pytz/zoneinfo/NZ',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/NZ',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kaliningrad',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kaliningrad',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Cordoba',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/America/Santarem',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Santarem',
   'DATA'),
  ('pytz/zoneinfo/Africa/Abidjan',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Abidjan',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Easter',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Easter',
   'DATA'),
  ('pytz/zoneinfo/Iran',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Iran',
   'DATA'),
  ('pytz/zoneinfo/Indian/Maldives',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Maldives',
   'DATA'),
  ('pytz/zoneinfo/America/Jujuy',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/GMT',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/GMT',
   'DATA'),
  ('pytz/zoneinfo/Arctic/Longyearbyen',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Arctic/Longyearbyen',
   'DATA'),
  ('pytz/zoneinfo/America/Thule',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Thule',
   'DATA'),
  ('pytz/zoneinfo/America/Port_of_Spain',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Port_of_Spain',
   'DATA'),
  ('pytz/zoneinfo/Asia/Irkutsk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Irkutsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Niamey',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Niamey',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-6',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-6',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Palau',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Palau',
   'DATA'),
  ('pytz/zoneinfo/Asia/Atyrau',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Atyrau',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bishkek',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bishkek',
   'DATA'),
  ('pytz/zoneinfo/America/Managua',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Managua',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-1',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-1',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bujumbura',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bujumbura',
   'DATA'),
  ('pytz/zoneinfo/EST',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/EST',
   'DATA'),
  ('pytz/zoneinfo/Europe/Nicosia',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Zulu',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Zulu',
   'DATA'),
  ('pytz/zoneinfo/US/Pacific',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/US/Pacific',
   'DATA'),
  ('pytz/zoneinfo/America/Pangnirtung',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Pangnirtung',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashkhabad',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ashkhabad',
   'DATA'),
  ('pytz/zoneinfo/Australia/LHI',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/LHI',
   'DATA'),
  ('pytz/zoneinfo/Asia/Katmandu',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Katmandu',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lusaka',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lusaka',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimphu',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Thimphu',
   'DATA'),
  ('pytz/zoneinfo/Canada/Pacific',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Pacific',
   'DATA'),
  ('pytz/zoneinfo/America/Campo_Grande',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Campo_Grande',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Cape_Verde',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Cape_Verde',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kosrae',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kosrae',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wallis',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Wallis',
   'DATA'),
  ('pytz/zoneinfo/America/Cuiaba',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Cuiaba',
   'DATA'),
  ('pytz/zoneinfo/America/Dominica',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Dominica',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yangon',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yangon',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-8',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-8',
   'DATA'),
  ('pytz/zoneinfo/Poland',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Poland',
   'DATA'),
  ('pytz/zoneinfo/America/Eirunepe',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Eirunepe',
   'DATA'),
  ('pytz/zoneinfo/Brazil/DeNoronha',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/DeNoronha',
   'DATA'),
  ('pytz/zoneinfo/Africa/Conakry',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Conakry',
   'DATA'),
  ('pytz/zoneinfo/Navajo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Navajo',
   'DATA'),
  ('pytz/zoneinfo/Indian/Reunion',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Reunion',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+8',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+8',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ulyanovsk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Ulyanovsk',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lord_Howe',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Lord_Howe',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Truk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Truk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Guernsey',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Guernsey',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+9',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+9',
   'DATA'),
  ('pytz/zoneinfo/America/Kralendijk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Kralendijk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Accra',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Accra',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vatican',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vatican',
   'DATA'),
  ('pytz/zoneinfo/Africa/Windhoek',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Windhoek',
   'DATA'),
  ('pytz/zoneinfo/Asia/Harbin',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Harbin',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nouakchott',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Nouakchott',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hebron',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hebron',
   'DATA'),
  ('pytz/zoneinfo/Hongkong',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Hongkong',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Enderbury',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Enderbury',
   'DATA'),
  ('pytz/zoneinfo/America/Edmonton',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Edmonton',
   'DATA'),
  ('pytz/zoneinfo/America/Atka',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Atka',
   'DATA'),
  ('pytz/zoneinfo/Asia/Beirut',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Beirut',
   'DATA'),
  ('pytz/zoneinfo/America/Halifax',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Halifax',
   'DATA'),
  ('pytz/zoneinfo/America/Buenos_Aires',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/America/Montserrat',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Montserrat',
   'DATA'),
  ('pytz/zoneinfo/Africa/El_Aaiun',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/El_Aaiun',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maseru',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Maseru',
   'DATA'),
  ('pytz/zoneinfo/America/Manaus',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Manaus',
   'DATA'),
  ('pytz/zoneinfo/America/St_Kitts',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Kitts',
   'DATA'),
  ('pytz/zoneinfo/Etc/UTC',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/UTC',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtobe',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aqtobe',
   'DATA'),
  ('pytz/zoneinfo/Iceland',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Iceland',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+12',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+12',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kirov',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kirov',
   'DATA'),
  ('pytz/zoneinfo/Asia/Amman',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Amman',
   'DATA'),
  ('pytz/zoneinfo/Africa/Brazzaville',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Brazzaville',
   'DATA'),
  ('pytz/zoneinfo/America/Regina',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Regina',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/McMurdo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/McMurdo',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Center',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/Center',
   'DATA'),
  ('pytz/zoneinfo/Europe/Gibraltar',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Gibraltar',
   'DATA'),
  ('pytz/zoneinfo/Asia/Gaza',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Gaza',
   'DATA'),
  ('pytz/zoneinfo/America/Thunder_Bay',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Thunder_Bay',
   'DATA'),
  ('pytz/zoneinfo/GMT+0',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Salta',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Salta',
   'DATA'),
  ('pytz/zoneinfo/MST',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/MST',
   'DATA'),
  ('pytz/zoneinfo/Africa/Timbuktu',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Timbuktu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Phnom_Penh',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Phnom_Penh',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Jan_Mayen',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Jan_Mayen',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sofia',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Sofia',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Dawson',
   'DATA'),
  ('pytz/zoneinfo/Asia/Karachi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Karachi',
   'DATA'),
  ('pytz/zoneinfo/Africa/Sao_Tome',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Sao_Tome',
   'DATA'),
  ('pytz/zoneinfo/Indian/Christmas',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Christmas',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Palmer',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Palmer',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maputo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Maputo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Oral',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Oral',
   'DATA'),
  ('pytz/zoneinfo/America/Paramaribo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Paramaribo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Istanbul',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/GMT-0',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dacca',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dacca',
   'DATA'),
  ('pytz/zoneinfo/Canada/Newfoundland',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Newfoundland',
   'DATA'),
  ('pytz/zoneinfo/Europe/Athens',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Athens',
   'DATA'),
  ('pytz/zoneinfo/America/St_Vincent',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Vincent',
   'DATA'),
  ('pytz/zoneinfo/America/Panama',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Panama',
   'DATA'),
  ('pytz/zoneinfo/America/Havana',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Havana',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tiraspol',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tiraspol',
   'DATA'),
  ('pytz/zoneinfo/America/Creston',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Creston',
   'DATA'),
  ('pytz/zoneinfo/PRC',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/PRC',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+5',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+5',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tehran',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tehran',
   'DATA'),
  ('pytz/zoneinfo/Asia/Sakhalin',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Sakhalin',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dushanbe',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dushanbe',
   'DATA'),
  ('pytz/zoneinfo/Australia/Queensland',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Queensland',
   'DATA'),
  ('pytz/zoneinfo/Europe/Busingen',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Busingen',
   'DATA'),
  ('pytz/zoneinfo/CET',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/CET',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Ushuaia',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Ushuaia',
   'DATA'),
  ('pytz/zoneinfo/America/Knox_IN',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Knox_IN',
   'DATA'),
  ('pytz/zoneinfo/America/Merida',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Merida',
   'DATA'),
  ('pytz/zoneinfo/Africa/Monrovia',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Monrovia',
   'DATA'),
  ('pytz/zoneinfo/Australia/Hobart',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Hobart',
   'DATA'),
  ('pytz/zoneinfo/America/Nassau',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Nassau',
   'DATA'),
  ('pytz/zoneinfo/Asia/Samarkand',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Samarkand',
   'DATA'),
  ('pytz/zoneinfo/Asia/Seoul',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Seoul',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Beulah',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/Beulah',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pitcairn',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pitcairn',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tunis',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Tunis',
   'DATA'),
  ('pytz/zoneinfo/America/Recife',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Recife',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmara',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Asmara',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faroe',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Faroe',
   'DATA'),
  ('pytz/zoneinfo/America/Curacao',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Curacao',
   'DATA'),
  ('pytz/zoneinfo/Europe/Mariehamn',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Mariehamn',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vilnius',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vilnius',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Indianapolis',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Europe/London',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/London',
   'DATA'),
  ('pytz/zoneinfo/Asia/Brunei',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Brunei',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tbilisi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tbilisi',
   'DATA'),
  ('pytz/zoneinfo/Indian/Kerguelen',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Kerguelen',
   'DATA'),
  ('pytz/zoneinfo/America/Port-au-Prince',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Port-au-Prince',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT',
   'DATA'),
  ('pytz/zoneinfo/Europe/Malta',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Malta',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-12',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-12',
   'DATA'),
  ('pytz/zoneinfo/Africa/Addis_Ababa',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Addis_Ababa',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Troll',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Troll',
   'DATA'),
  ('pytz/zoneinfo/GMT0',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/GMT0',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vevay',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Vevay',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Ponape',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Ponape',
   'DATA'),
  ('pytz/zoneinfo/W-SU',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/W-SU',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/DumontDUrville',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/DumontDUrville',
   'DATA'),
  ('pytz/zoneinfo/Asia/Krasnoyarsk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Krasnoyarsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nairobi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Nairobi',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+4',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+4',
   'DATA'),
  ('pytz/zoneinfo/US/Michigan',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/US/Michigan',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zurich',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zurich',
   'DATA'),
  ('pytz/zoneinfo/America/Aruba',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Aruba',
   'DATA'),
  ('pytz/zoneinfo/America/Araguaina',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Araguaina',
   'DATA'),
  ('pytz/zoneinfo/America/Cancun',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Cancun',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Port_Moresby',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Port_Moresby',
   'DATA'),
  ('pytz/zoneinfo/ROK',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/ROK',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zagreb',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zagreb',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kabul',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kabul',
   'DATA'),
  ('pytz/zoneinfo/Asia/Famagusta',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Famagusta',
   'DATA'),
  ('pytz/zoneinfo/zone1970.tab',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/zone1970.tab',
   'DATA'),
  ('pytz/zoneinfo/Africa/Johannesburg',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Johannesburg',
   'DATA'),
  ('pytz/zoneinfo/America/Antigua',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Antigua',
   'DATA'),
  ('pytz/zoneinfo/America/Iqaluit',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Iqaluit',
   'DATA'),
  ('pytz/zoneinfo/America/Ciudad_Juarez',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Ciudad_Juarez',
   'DATA'),
  ('pytz/zoneinfo/America/Asuncion',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Asuncion',
   'DATA'),
  ('pytz/zoneinfo/Africa/Libreville',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Libreville',
   'DATA'),
  ('pytz/zoneinfo/Africa/Banjul',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Banjul',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Reykjavik',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Reykjavik',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Macquarie',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Macquarie',
   'DATA'),
  ('pytz/zoneinfo/Africa/Luanda',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Luanda',
   'DATA'),
  ('pytz/zoneinfo/America/Goose_Bay',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Goose_Bay',
   'DATA'),
  ('pytz/zoneinfo/America/Louisville',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Louisville',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Tell_City',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Tell_City',
   'DATA'),
  ('pytz/zoneinfo/Turkey',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Turkey',
   'DATA'),
  ('pytz/zoneinfo/US/Eastern',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/US/Eastern',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chuuk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Chuuk',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Petersburg',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Petersburg',
   'DATA'),
  ('pytz/zoneinfo/Universal',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Universal',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Saipan',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Saipan',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/South_Georgia',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/South_Georgia',
   'DATA'),
  ('pytz/zoneinfo/America/Nuuk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Nuuk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Singapore',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Singapore',
   'DATA'),
  ('pytz/zoneinfo/America/Godthab',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Godthab',
   'DATA'),
  ('pytz/zoneinfo/Europe/Jersey',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Jersey',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kwajalein',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaNorte',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/BajaNorte',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Norfolk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Norfolk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Gaborone',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Gaborone',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Buenos_Aires',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia_Banderas',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Bahia_Banderas',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/New_Salem',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/New_Salem',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Rothera',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Rothera',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bucharest',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Bucharest',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/St_Helena',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/St_Helena',
   'DATA'),
  ('pytz/zoneinfo/Australia/Broken_Hill',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Broken_Hill',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Auckland',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Auckland',
   'DATA'),
  ('pytz/zoneinfo/America/Boa_Vista',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Boa_Vista',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ho_Chi_Minh',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ho_Chi_Minh',
   'DATA'),
  ('pytz/zoneinfo/America/Virgin',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Virgin',
   'DATA'),
  ('pytz/zoneinfo/Brazil/Acre',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/Acre',
   'DATA'),
  ('pytz/zoneinfo/Europe/Rome',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Rome',
   'DATA'),
  ('pytz/zoneinfo/Africa/Harare',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Harare',
   'DATA'),
  ('pytz/zoneinfo/America/Fortaleza',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Fortaleza',
   'DATA'),
  ('pytz/zoneinfo/America/Vancouver',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Vancouver',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Midway',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Midway',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jerusalem',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jerusalem',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pontianak',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Pontianak',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vaduz',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vaduz',
   'DATA'),
  ('pytz/zoneinfo/Europe/Chisinau',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Chisinau',
   'DATA'),
  ('pytz/zoneinfo/UTC',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/UTC',
   'DATA'),
  ('pytz/zoneinfo/America/Catamarca',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Europe/Berlin',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Berlin',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hong_Kong',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hong_Kong',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tirane',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tirane',
   'DATA'),
  ('pytz/zoneinfo/Asia/Barnaul',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Barnaul',
   'DATA'),
  ('pytz/zoneinfo/Egypt',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Egypt',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashgabat',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ashgabat',
   'DATA'),
  ('pytz/zoneinfo/America/Montreal',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Montreal',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bahrain',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bahrain',
   'DATA'),
  ('pytz/zoneinfo/America/Rio_Branco',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Rio_Branco',
   'DATA'),
  ('pytz/zoneinfo/Europe/Istanbul',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/Europe/Copenhagen',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Copenhagen',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Niue',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Niue',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulaanbaatar',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ulaanbaatar',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belfast',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Belfast',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bamako',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bamako',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Canary',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Canary',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lagos',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lagos',
   'DATA'),
  ('pytz/zoneinfo/America/Cayman',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Cayman',
   'DATA'),
  ('pytz/zoneinfo/America/Barbados',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Barbados',
   'DATA'),
  ('pytz/zoneinfo/America/Mexico_City',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Mexico_City',
   'DATA'),
  ('pytz/zoneinfo/America/Coyhaique',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Coyhaique',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ouagadougou',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ouagadougou',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macau',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Macau',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Galapagos',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Galapagos',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kanton',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kanton',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Catamarca',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/America/Lower_Princes',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Lower_Princes',
   'DATA'),
  ('pytz/zoneinfo/Europe/Prague',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Prague',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tripoli',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Tripoli',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ndjamena',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ndjamena',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chongqing',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chongqing',
   'DATA'),
  ('pytz/zoneinfo/Eire',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Eire',
   'DATA'),
  ('pytz/zoneinfo/America/Ensenada',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Ensenada',
   'DATA'),
  ('pytz/zoneinfo/Australia/NSW',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/NSW',
   'DATA'),
  ('pytz/zoneinfo/Indian/Chagos',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Chagos',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bissau',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bissau',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/La_Rioja',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/La_Rioja',
   'DATA'),
  ('pytz/zoneinfo/Europe/Madrid',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Madrid',
   'DATA'),
  ('pytz/zoneinfo/Canada/Yukon',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Yukon',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tallinn',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tallinn',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+1',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+1',
   'DATA'),
  ('pytz/zoneinfo/America/Hermosillo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Hermosillo',
   'DATA'),
  ('pytz/zoneinfo/GB-Eire',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/GB-Eire',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Nauru',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Nauru',
   'DATA'),
  ('pytz/zoneinfo/America/Sitka',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Sitka',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Mawson',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Mawson',
   'DATA'),
  ('pytz/zoneinfo/Canada/Eastern',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Eastern',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtau',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aqtau',
   'DATA'),
  ('pytz/zoneinfo/Europe/Andorra',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Andorra',
   'DATA'),
  ('pytz/zoneinfo/America/Martinique',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Martinique',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Winamac',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Winamac',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-0',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/US/Aleutian',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/US/Aleutian',
   'DATA'),
  ('pytz/zoneinfo/US/Hawaii',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/US/Hawaii',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Bougainville',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Bougainville',
   'DATA'),
  ('pytz/zoneinfo/HST',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/HST',
   'DATA'),
  ('pytz/zoneinfo/America/Santa_Isabel',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Santa_Isabel',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Noumea',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Noumea',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Acre',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Porto_Acre',
   'DATA'),
  ('pytz/zoneinfo/America/Guayaquil',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Guayaquil',
   'DATA'),
  ('pytz/zoneinfo/America/St_Johns',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Johns',
   'DATA'),
  ('pytz/zoneinfo/America/Danmarkshavn',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Danmarkshavn',
   'DATA'),
  ('pytz/zoneinfo/Europe/Helsinki',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Helsinki',
   'DATA'),
  ('pytz/zoneinfo/Europe/San_Marino',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/San_Marino',
   'DATA'),
  ('pytz/zoneinfo/America/Santo_Domingo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Santo_Domingo',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Efate',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Efate',
   'DATA'),
  ('pytz/zoneinfo/America/Toronto',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Toronto',
   'DATA'),
  ('pytz/zoneinfo/Europe/Lisbon',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Lisbon',
   'DATA'),
  ('pytz/zoneinfo/Asia/Khandyga',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Khandyga',
   'DATA'),
  ('pytz/zoneinfo/America/Atikokan',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Atikokan',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Monticello',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Kentucky/Monticello',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ujung_Pandang',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ujung_Pandang',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kigali',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kigali',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimbu',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Thimbu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Astrakhan',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Astrakhan',
   'DATA'),
  ('pytz/zoneinfo/Australia/Adelaide',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Adelaide',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-5',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-5',
   'DATA'),
  ('pytz/zoneinfo/Etc/Universal',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Universal',
   'DATA'),
  ('pytz/zoneinfo/America/Guatemala',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Guatemala',
   'DATA'),
  ('pytz/zoneinfo/America/Marigot',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Marigot',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qatar',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qatar',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ljubljana',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Ljubljana',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Rarotonga',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Rarotonga',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tahiti',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tahiti',
   'DATA'),
  ('pytz/zoneinfo/America/Rankin_Inlet',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Rankin_Inlet',
   'DATA'),
  ('pytz/zoneinfo/Asia/Urumqi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Urumqi',
   'DATA'),
  ('pytz/zoneinfo/Indian/Antananarivo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Antananarivo',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chatham',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Chatham',
   'DATA'),
  ('pytz/zoneinfo/America/Noronha',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Noronha',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Velho',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Porto_Velho',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baghdad',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Baghdad',
   'DATA'),
  ('pytz/zoneinfo/America/Puerto_Rico',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Puerto_Rico',
   'DATA'),
  ('pytz/zoneinfo/America/Anchorage',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Anchorage',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/South_Pole',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/South_Pole',
   'DATA'),
  ('pytz/zoneinfo/America/New_York',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/New_York',
   'DATA'),
  ('pytz/zoneinfo/Asia/Rangoon',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Rangoon',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dakar',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Dakar',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-9',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-9',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qostanay',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qostanay',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-7',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-7',
   'DATA'),
  ('pytz/zoneinfo/Australia/Brisbane',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Brisbane',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fakaofo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Fakaofo',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+7',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+7',
   'DATA'),
  ('pytz/zoneinfo/US/Mountain',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/US/Mountain',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-10',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-10',
   'DATA'),
  ('pytz/zoneinfo/America/Chicago',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Chicago',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/ComodRivadavia',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/ComodRivadavia',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Casey',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Casey',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+3',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+3',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mbabane',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Mbabane',
   'DATA'),
  ('pytz/zoneinfo/Africa/Blantyre',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Blantyre',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Madeira',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Madeira',
   'DATA'),
  ('pytz/zoneinfo/Europe/Podgorica',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Podgorica',
   'DATA'),
  ('pytz/zoneinfo/Indian/Comoro',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Comoro',
   'DATA'),
  ('pytz/zoneinfo/Europe/Dublin',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Dublin',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vientiane',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Vientiane',
   'DATA'),
  ('pytz/zoneinfo/Asia/Saigon',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Saigon',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+0',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sarajevo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Sarajevo',
   'DATA'),
  ('pytz/zoneinfo/Singapore',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Singapore',
   'DATA'),
  ('pytz/zoneinfo/US/Indiana-Starke',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/US/Indiana-Starke',
   'DATA'),
  ('pytz/zoneinfo/America/Scoresbysund',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Scoresbysund',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-13',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-13',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-3',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-3',
   'DATA'),
  ('pytz/zoneinfo/Europe/Monaco',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Monaco',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yerevan',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yerevan',
   'DATA'),
  ('pytz/zoneinfo/Brazil/West',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/West',
   'DATA'),
  ('pytz/zoneinfo/America/Detroit',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Detroit',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tokyo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tokyo',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kyiv',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kyiv',
   'DATA'),
  ('pytz/zoneinfo/America/Shiprock',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Shiprock',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Wayne',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Fort_Wayne',
   'DATA'),
  ('pytz/zoneinfo/America/Maceio',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Maceio',
   'DATA'),
  ('pytz/zoneinfo/Europe/Paris',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Paris',
   'DATA'),
  ('pytz/zoneinfo/America/Tortola',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Tortola',
   'DATA'),
  ('pytz/zoneinfo/Europe/Simferopol',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Simferopol',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vincennes',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Vincennes',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baku',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Baku',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Bermuda',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Bermuda',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dubai',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dubai',
   'DATA'),
  ('pytz/zoneinfo/Africa/Khartoum',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Khartoum',
   'DATA'),
  ('pytz/zoneinfo/Jamaica',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wake',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Wake',
   'DATA'),
  ('pytz/zoneinfo/America/Indianapolis',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Africa/Douala',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Douala',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yekaterinburg',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yekaterinburg',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jakarta',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jakarta',
   'DATA'),
  ('pytz/zoneinfo/Asia/Nicosia',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mahe',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mahe',
   'DATA'),
  ('pytz/zoneinfo/Canada/Saskatchewan',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Saskatchewan',
   'DATA'),
  ('pytz/zoneinfo/America/Rosario',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Rosario',
   'DATA'),
  ('pytz/zoneinfo/NZ-CHAT',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/NZ-CHAT',
   'DATA'),
  ('pytz/zoneinfo/Canada/Atlantic',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Atlantic',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Bahia',
   'DATA'),
  ('pytz/zoneinfo/Africa/Cairo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Cairo',
   'DATA'),
  ('pytz/zoneinfo/America/Montevideo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Montevideo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macao',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Macao',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Knox',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Knox',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Marengo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Marengo',
   'DATA'),
  ('pytz/zoneinfo/Australia/Tasmania',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Tasmania',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-14',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-14',
   'DATA'),
  ('pytz/zoneinfo/Australia/Yancowinna',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Yancowinna',
   'DATA'),
  ('pytz/zoneinfo/Europe/Riga',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Riga',
   'DATA'),
  ('pytz/zoneinfo/America/Jamaica',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/America/Tegucigalpa',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Tegucigalpa',
   'DATA'),
  ('pytz/zoneinfo/America/Denver',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Denver',
   'DATA'),
  ('pytz/zoneinfo/Africa/Juba',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Juba',
   'DATA'),
  ('pytz/zoneinfo/America/Punta_Arenas',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Punta_Arenas',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Johnston',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Johnston',
   'DATA'),
  ('pytz/zoneinfo/America/Yakutat',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Yakutat',
   'DATA'),
  ('pytz/zoneinfo/leapseconds',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/leapseconds',
   'DATA'),
  ('pytz/zoneinfo/MST7MDT',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/MST7MDT',
   'DATA'),
  ('pytz/zoneinfo/Etc/Greenwich',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/Australia/Melbourne',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Melbourne',
   'DATA'),
  ('pytz/zoneinfo/Australia/Sydney',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Sydney',
   'DATA'),
  ('pytz/zoneinfo/Australia/Currie',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Currie',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmera',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Asmera',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Louisville',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Kentucky/Louisville',
   'DATA'),
  ('pytz/zoneinfo/Asia/Calcutta',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Calcutta',
   'DATA'),
  ('pytz/zoneinfo/Asia/Anadyr',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Anadyr',
   'DATA'),
  ('pytz/zoneinfo/Europe/Skopje',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Skopje',
   'DATA'),
  ('pytz/zoneinfo/Asia/Makassar',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Makassar',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Yap',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Yap',
   'DATA'),
  ('pytz/zoneinfo/Europe/Moscow',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Moscow',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hovd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hovd',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mauritius',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mauritius',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Gambier',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Gambier',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pago_Pago',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pago_Pago',
   'DATA'),
  ('pytz/zoneinfo/Cuba',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Cuba',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dili',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dili',
   'DATA'),
  ('pytz/zoneinfo/Chile/Continental',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Chile/Continental',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tongatapu',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tongatapu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Budapest',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Budapest',
   'DATA'),
  ('pytz/zoneinfo/America/Sao_Paulo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Sao_Paulo',
   'DATA'),
  ('pytz/zoneinfo/America/Chihuahua',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Chihuahua',
   'DATA'),
  ('pytz/zoneinfo/America/Costa_Rica',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Costa_Rica',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zaporozhye',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zaporozhye',
   'DATA'),
  ('pytz/zoneinfo/US/Alaska',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/US/Alaska',
   'DATA'),
  ('pytz/zoneinfo/America/Resolute',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Resolute',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+2',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+2',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lubumbashi',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lubumbashi',
   'DATA'),
  ('pytz/zoneinfo/America/Cayenne',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Cayenne',
   'DATA'),
  ('pytz/zoneinfo/UCT',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/UCT',
   'DATA'),
  ('pytz/zoneinfo/CST6CDT',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/CST6CDT',
   'DATA'),
  ('pytz/zoneinfo/Australia/ACT',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/ACT',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Marquesas',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Marquesas',
   'DATA'),
  ('pytz/zoneinfo/America/Mendoza',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/zone.tab',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/zone.tab',
   'DATA'),
  ('pytz/zoneinfo/America/St_Barthelemy',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Barthelemy',
   'DATA'),
  ('pytz/zoneinfo/Europe/Uzhgorod',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Uzhgorod',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Apia',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Apia',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson_Creek',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Dawson_Creek',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guam',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Guam',
   'DATA'),
  ('pytz/zoneinfo/Greenwich',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Davis',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Davis',
   'DATA'),
  ('pytz/zoneinfo/Africa/Djibouti',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Djibouti',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Stanley',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Stanley',
   'DATA'),
  ('pytz/zoneinfo/Asia/Muscat',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Muscat',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kashgar',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kashgar',
   'DATA'),
  ('pytz/zoneinfo/Europe/Oslo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Oslo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lome',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lome',
   'DATA'),
  ('pytz/zoneinfo/America/Yellowknife',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Yellowknife',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Jujuy',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/America/Lima',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Lima',
   'DATA'),
  ('pytz/zoneinfo/America/Coral_Harbour',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Coral_Harbour',
   'DATA'),
  ('pytz/zoneinfo/America/Swift_Current',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Swift_Current',
   'DATA'),
  ('pytz/zoneinfo/Asia/Magadan',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Magadan',
   'DATA'),
  ('pytz/zoneinfo/Canada/Central',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Central',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bratislava',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Bratislava',
   'DATA'),
  ('pytz/zoneinfo/Asia/Colombo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Colombo',
   'DATA'),
  ('pytz/zoneinfo/Mexico/General',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/General',
   'DATA'),
  ('pytz/zoneinfo/US/Central',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/US/Central',
   'DATA'),
  ('pytz/zoneinfo/Australia/Darwin',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Darwin',
   'DATA'),
  ('pytz/zoneinfo/Europe/Luxembourg',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Luxembourg',
   'DATA'),
  ('pytz/zoneinfo/America/Rainy_River',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Rainy_River',
   'DATA'),
  ('pytz/zoneinfo/Asia/Manila',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Manila',
   'DATA'),
  ('pytz/zoneinfo/Africa/Malabo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Malabo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ceuta',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ceuta',
   'DATA'),
  ('pytz/zoneinfo/Etc/UCT',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/UCT',
   'DATA'),
  ('pytz/zoneinfo/EST5EDT',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/EST5EDT',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-4',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-4',
   'DATA'),
  ('pytz/zoneinfo/America/Juneau',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Juneau',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Nelson',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Fort_Nelson',
   'DATA'),
  ('pytz/zoneinfo/Asia/Srednekolymsk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Srednekolymsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mogadishu',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Mogadishu',
   'DATA'),
  ('pytz/zoneinfo/America/Caracas',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Caracas',
   'DATA'),
  ('pytz/zoneinfo/Libya',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Libya',
   'DATA'),
  ('pytz/zoneinfo/America/Adak',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Adak',
   'DATA'),
  ('pytz/zoneinfo/America/Phoenix',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Phoenix',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Samoa',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Majuro',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Majuro',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tarawa',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tarawa',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Syowa',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Syowa',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vienna',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vienna',
   'DATA'),
  ('pytz/zoneinfo/Factory',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Factory',
   'DATA'),
  ('pytz/zoneinfo/Portugal',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Portugal',
   'DATA'),
  ('pytz/zoneinfo/America/Anguilla',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Anguilla',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yakutsk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yakutsk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Taipei',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Taipei',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kamchatka',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kamchatka',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kinshasa',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kinshasa',
   'DATA'),
  ('pytz/zoneinfo/EET',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/EET',
   'DATA'),
  ('pytz/zoneinfo/America/Matamoros',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Matamoros',
   'DATA'),
  ('pytz/zoneinfo/America/Menominee',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Menominee',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Juan',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/San_Juan',
   'DATA'),
  ('pytz/zoneinfo/Africa/Freetown',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Freetown',
   'DATA'),
  ('pytz/zoneinfo/America/Belem',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Belem',
   'DATA'),
  ('pytz/zoneinfo/America/Inuvik',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Inuvik',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Tucuman',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Tucuman',
   'DATA'),
  ('pytz/zoneinfo/Japan',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Japan',
   'DATA'),
  ('pytz/zoneinfo/Canada/Mountain',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Mountain',
   'DATA'),
  ('pytz/zoneinfo/Australia/South',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/South',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuwait',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuwait',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Honolulu',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Honolulu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chungking',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chungking',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mayotte',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mayotte',
   'DATA'),
  ('pytz/zoneinfo/America/Cambridge_Bay',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Cambridge_Bay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Saratov',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Saratov',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kampala',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kampala',
   'DATA'),
  ('pytz/zoneinfo/Asia/Damascus',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Damascus',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Mendoza',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Azores',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Azores',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bangui',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bangui',
   'DATA'),
  ('pytz/zoneinfo/America/Glace_Bay',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Glace_Bay',
   'DATA'),
  ('pytz/zoneinfo/America/St_Thomas',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Thomas',
   'DATA'),
  ('pytz/zoneinfo/Asia/Choibalsan',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Choibalsan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vladivostok',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Vladivostok',
   'DATA'),
  ('pytz/zoneinfo/Australia/Canberra',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Canberra',
   'DATA'),
  ('pytz/zoneinfo/America/Whitehorse',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Whitehorse',
   'DATA'),
  ('pytz/zoneinfo/America/Nipigon',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Nipigon',
   'DATA'),
  ('pytz/zoneinfo/Europe/Minsk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Minsk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Shanghai',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Shanghai',
   'DATA'),
  ('pytz/zoneinfo/US/East-Indiana',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/US/East-Indiana',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+6',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+6',
   'DATA'),
  ('pytz/zoneinfo/Africa/Casablanca',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Casablanca',
   'DATA'),
  ('pytz/zoneinfo/Asia/Omsk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Omsk',
   'DATA'),
  ('pytz/zoneinfo/PST8PDT',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/PST8PDT',
   'DATA'),
  ('pytz/zoneinfo/Brazil/East',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/East',
   'DATA'),
  ('pytz/zoneinfo/Kwajalein',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/Australia/West',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/West',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qyzylorda',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qyzylorda',
   'DATA'),
  ('pytz/zoneinfo/Africa/Algiers',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Algiers',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chita',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chita',
   'DATA'),
  ('pytz/zoneinfo/zonenow.tab',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/zonenow.tab',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faeroe',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Faeroe',
   'DATA'),
  ('pytz/zoneinfo/America/Ojinaga',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Ojinaga',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kiritimati',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kiritimati',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Funafuti',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Funafuti',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dar_es_Salaam',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Dar_es_Salaam',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guadalcanal',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Guadalcanal',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aden',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aden',
   'DATA'),
  ('pytz/zoneinfo/iso3166.tab',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/iso3166.tab',
   'DATA'),
  ('pytz/zoneinfo/America/Miquelon',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Miquelon',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pohnpei',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pohnpei',
   'DATA'),
  ('pytz/zoneinfo/Australia/Perth',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Perth',
   'DATA'),
  ('pytz/zoneinfo/America/El_Salvador',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/El_Salvador',
   'DATA'),
  ('pytz/zoneinfo/America/Moncton',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Moncton',
   'DATA'),
  ('pytz/zoneinfo/America/Belize',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Belize',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kiev',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kiev',
   'DATA'),
  ('pytz/zoneinfo/ROC',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/ROC',
   'DATA'),
  ('pytz/zoneinfo/Europe/Isle_of_Man',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Isle_of_Man',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-2',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-2',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kathmandu',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kathmandu',
   'DATA'),
  ('pytz/zoneinfo/America/Metlakatla',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Metlakatla',
   'DATA'),
  ('pytz/zoneinfo/America/St_Lucia',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Lucia',
   'DATA'),
  ('pytz/zoneinfo/US/Samoa',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/US/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novokuznetsk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Novokuznetsk',
   'DATA'),
  ('pytz/zoneinfo/WET',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/WET',
   'DATA'),
  ('pytz/zoneinfo/Europe/Stockholm',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Stockholm',
   'DATA'),
  ('pytz/zoneinfo/Africa/Porto-Novo',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Porto-Novo',
   'DATA'),
  ('pytz/zoneinfo/Europe/Volgograd',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Volgograd',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+11',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+11',
   'DATA'),
  ('pytz/zoneinfo/America/Bogota',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Bogota',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tel_Aviv',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tel_Aviv',
   'DATA'),
  ('pytz/zoneinfo/Etc/Zulu',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Zulu',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Vostok',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Vostok',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+10',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+10',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pyongyang',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Pyongyang',
   'DATA'),
  ('pytz/zoneinfo/Indian/Cocos',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Cocos',
   'DATA'),
  ('pytz/zoneinfo/America/Grenada',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Grenada',
   'DATA'),
  ('pytz/zoneinfo/Australia/Victoria',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Victoria',
   'DATA'),
  ('pytz/zoneinfo/America/Santiago',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Santiago',
   'DATA'),
  ('pytz/zoneinfo/Asia/Almaty',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Almaty',
   'DATA'),
  ('pytz/zoneinfo/America/Cordoba',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fiji',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Fiji',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuching',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuching',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulan_Bator',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ulan_Bator',
   'DATA'),
  ('pytz/zoneinfo/Asia/Riyadh',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Riyadh',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tomsk',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tomsk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Samara',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Samara',
   'DATA'),
  ('pytz/zoneinfo/America/Mazatlan',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/Mazatlan',
   'DATA'),
  ('pytz/zoneinfo/America/La_Paz',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/pytz/zoneinfo/America/La_Paz',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/XSD2Schtrn.xsl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/isoschematron/resources/xsl/XSD2Schtrn.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_svrl_for_xslt1.xsl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_skeleton_for_xslt1.xsl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_dsdl_include.xsl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_dsdl_include.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/RNG2Schtrn.xsl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/isoschematron/resources/xsl/RNG2Schtrn.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_abstract_expand.xsl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_abstract_expand.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/readme.txt',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/readme.txt',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_message.xsl',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_message.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/rng/iso-schematron.rng',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/lxml/isoschematron/resources/rng/iso-schematron.rng',
   'DATA'),
  ('altair/vegalite/v5/schema/vega-themes.json',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/altair/vegalite/v5/schema/vega-themes.json',
   'DATA'),
  ('altair/jupyter/js/README.md',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/altair/jupyter/js/README.md',
   'DATA'),
  ('altair/vegalite/v5/schema/vega-lite-schema.json',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/altair/vegalite/v5/schema/vega-lite-schema.json',
   'DATA'),
  ('altair/py.typed',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/altair/py.typed',
   'DATA'),
  ('altair/jupyter/js/index.js',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/altair/jupyter/js/index.js',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/core',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/core',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/content',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/content',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/content',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/content',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/meta-data',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/meta-data',
   'DATA'),
  ('jsonschema_specifications/schemas/draft3/metaschema.json',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft3/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft7/metaschema.json',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft7/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/format',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/format',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/metaschema.json',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/validation',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/validation',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/unevaluated',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/unevaluated',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/applicator',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/applicator',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/core',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/core',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/metaschema.json',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft4/metaschema.json',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft4/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft6/metaschema.json',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft6/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/format-annotation',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/format-annotation',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/validation',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/validation',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/applicator',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/applicator',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/format-assertion',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/format-assertion',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/meta-data',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/meta-data',
   'DATA'),
  ('jsonschema-4.24.1.dist-info/WHEEL',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema-4.24.1.dist-info/WHEEL',
   'DATA'),
  ('jsonschema-4.24.1.dist-info/METADATA',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema-4.24.1.dist-info/METADATA',
   'DATA'),
  ('jsonschema-4.24.1.dist-info/licenses/COPYING',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema-4.24.1.dist-info/licenses/COPYING',
   'DATA'),
  ('jsonschema-4.24.1.dist-info/INSTALLER',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema-4.24.1.dist-info/INSTALLER',
   'DATA'),
  ('jsonschema/benchmarks/issue232/issue.json',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema/benchmarks/issue232/issue.json',
   'DATA'),
  ('jsonschema-4.24.1.dist-info/RECORD',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema-4.24.1.dist-info/RECORD',
   'DATA'),
  ('jsonschema-4.24.1.dist-info/entry_points.txt',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/jsonschema-4.24.1.dist-info/entry_points.txt',
   'DATA'),
  ('certifi/py.typed',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/certifi/py.typed',
   'DATA'),
  ('certifi/cacert.pem',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/certifi/cacert.pem',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.13/Python', 'SYMLINK'),
  ('numpy-2.3.1.dist-info/INSTALLER',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy-2.3.1.dist-info/INSTALLER',
   'DATA'),
  ('numpy-2.3.1.dist-info/LICENSE.txt',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy-2.3.1.dist-info/LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/INSTALLER',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info/INSTALLER',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/attrs-25.3.0.dist-info/INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info/INSTALLER',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/click-8.2.1.dist-info/INSTALLER',
   'DATA'),
  ('numpy-2.3.1.dist-info/METADATA',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy-2.3.1.dist-info/METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/RECORD',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info/WHEEL',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/attrs-25.3.0.dist-info/WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/METADATA',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/WHEEL',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info/RECORD',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/attrs-25.3.0.dist-info/RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info/WHEEL',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/click-8.2.1.dist-info/WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info/METADATA',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/click-8.2.1.dist-info/METADATA',
   'DATA'),
  ('click-8.2.1.dist-info/RECORD',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/click-8.2.1.dist-info/RECORD',
   'DATA'),
  ('numpy-2.3.1.dist-info/RECORD',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy-2.3.1.dist-info/RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info/METADATA',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/attrs-25.3.0.dist-info/METADATA',
   'DATA'),
  ('click-8.2.1.dist-info/licenses/LICENSE.txt',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/click-8.2.1.dist-info/licenses/LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/top_level.txt',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/top_level.txt',
   'DATA'),
  ('numpy-2.3.1.dist-info/WHEEL',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy-2.3.1.dist-info/WHEEL',
   'DATA'),
  ('numpy-2.3.1.dist-info/entry_points.txt',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/numpy-2.3.1.dist-info/entry_points.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info/licenses/LICENSE',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/attrs-25.3.0.dist-info/licenses/LICENSE',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/build/launch_app/base_library.zip',
   'DATA'),
  ('libarrow_compute.2100.dylib',
   'pyarrow/libarrow_compute.2100.dylib',
   'SYMLINK'),
  ('libparquet.2100.dylib', 'pyarrow/libparquet.2100.dylib', 'SYMLINK'),
  ('libarrow_dataset.2100.dylib',
   'pyarrow/libarrow_dataset.2100.dylib',
   'SYMLINK'),
  ('libarrow.2100.dylib', 'pyarrow/libarrow.2100.dylib', 'SYMLINK'),
  ('libarrow_acero.2100.dylib', 'pyarrow/libarrow_acero.2100.dylib', 'SYMLINK'),
  ('libarrow_python.2100.dylib',
   'pyarrow/libarrow_python.2100.dylib',
   'SYMLINK'),
  ('libarrow_substrait.2100.dylib',
   'pyarrow/libarrow_substrait.2100.dylib',
   'SYMLINK'),
  ('libarrow_flight.2100.dylib',
   'pyarrow/libarrow_flight.2100.dylib',
   'SYMLINK'),
  ('libarrow_python_parquet_encryption.2100.dylib',
   'pyarrow/libarrow_python_parquet_encryption.2100.dylib',
   'SYMLINK'),
  ('libarrow_python_flight.2100.dylib',
   'pyarrow/libarrow_python_flight.2100.dylib',
   'SYMLINK'),
  ('libwebp.7.dylib', 'PIL/.dylibs/libwebp.7.dylib', 'SYMLINK'),
  ('libwebpmux.3.dylib', 'PIL/.dylibs/libwebpmux.3.dylib', 'SYMLINK'),
  ('libwebpdemux.2.dylib', 'PIL/.dylibs/libwebpdemux.2.dylib', 'SYMLINK'),
  ('libavif.16.3.0.dylib', 'PIL/.dylibs/libavif.16.3.0.dylib', 'SYMLINK'),
  ('liblcms2.2.dylib', 'PIL/.dylibs/liblcms2.2.dylib', 'SYMLINK'),
  ('libxcb.1.1.0.dylib', 'PIL/.dylibs/libxcb.1.1.0.dylib', 'SYMLINK'),
  ('libjpeg.62.4.0.dylib', 'PIL/.dylibs/libjpeg.62.4.0.dylib', 'SYMLINK'),
  ('libtiff.6.dylib', 'PIL/.dylibs/libtiff.6.dylib', 'SYMLINK'),
  ('libz.1.3.1.zlib-ng.dylib',
   'PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'SYMLINK'),
  ('libopenjp2.2.5.3.dylib', 'PIL/.dylibs/libopenjp2.2.5.3.dylib', 'SYMLINK'),
  ('libsharpyuv.0.dylib', 'PIL/.dylibs/libsharpyuv.0.dylib', 'SYMLINK'),
  ('libXau.6.dylib', 'PIL/.dylibs/libXau.6.dylib', 'SYMLINK'),
  ('liblzma.5.dylib', 'PIL/.dylibs/liblzma.5.dylib', 'SYMLINK'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.13/Resources/Info.plist',
   '/Library/Frameworks/Python.framework/Versions/3.13/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.13', 'SYMLINK')],
 [],
 False,
 False,
 1753970367,
 [('runw',
   '/Users/<USER>/Desktop/AI '
   'Tutorials/storyboard/.venv/lib/python3.13/site-packages/PyInstaller/bootloader/Darwin-64bit/runw',
   'EXECUTABLE')],
 '/Library/Frameworks/Python.framework/Versions/3.13/Python')
