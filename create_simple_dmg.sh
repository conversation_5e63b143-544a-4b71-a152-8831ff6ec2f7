#!/bin/bash

# Simple script to create a DMG file for the Storyboard Generator app
# This version avoids complex mounting and customization

set -e  # Exit on any error

APP_NAME="Storyboard Generator"
DMG_NAME="StoryboardGenerator"
VERSION="1.0.0"
APP_PATH="dist/StoryboardGenerator.app"
DMG_PATH="${DMG_NAME}.dmg"

echo "🚀 Creating DMG for ${APP_NAME} v${VERSION}"

# Check if the app exists
if [ ! -d "$APP_PATH" ]; then
    echo "❌ Error: App not found at $APP_PATH"
    echo "Please build the app first using: pyinstaller launch_app.spec"
    exit 1
fi

# Clean up any existing DMG files
echo "🧹 Cleaning up existing files..."
rm -f "$DMG_PATH"

# Create a temporary directory for DMG contents
TEMP_DIR=$(mktemp -d)
echo "📁 Created temporary directory: $TEMP_DIR"

# Copy the app to temp directory
echo "📦 Copying app to temporary directory..."
cp -R "$APP_PATH" "$TEMP_DIR/"

# Copy README and other documentation
echo "📄 Adding documentation..."
cp README.md "$TEMP_DIR/"

# Create a simple license file if it doesn't exist
if [ ! -f "LICENSE" ]; then
    cat > "$TEMP_DIR/LICENSE" << EOF
MIT License

Copyright (c) 2024 Storyboard Generator

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
EOF
else
    cp LICENSE "$TEMP_DIR/"
fi

# Create a setup instructions file
cat > "$TEMP_DIR/Setup Instructions.txt" << EOF
Storyboard Generator Setup Instructions
======================================

1. INSTALL THE APP:
   - Drag "StoryboardGenerator.app" to your Applications folder
   - You may need to right-click and select "Open" the first time due to macOS security

2. SETUP API KEY:
   Before using the app, you need to set up your OpenAI API key:
   
   Option A - Using Terminal:
   Open Terminal and run:
   export OPENAI_API_KEY="your_api_key_here"
   
   Option B - Create .env file:
   Navigate to the app's directory and create a .env file with:
   OPENAI_API_KEY=your_api_key_here

3. GET AN OPENAI API KEY:
   - Visit https://platform.openai.com/api-keys
   - Create an account or sign in
   - Generate a new API key
   - Make sure you have credits in your OpenAI account

4. LAUNCH THE APP:
   - Open from Applications folder or Launchpad
   - The app will start automatically

5. TROUBLESHOOTING:
   - If the app doesn't open, try right-clicking and selecting "Open"
   - Check that your API key is correctly set
   - Ensure you have an internet connection

For more detailed instructions, see README.md

Enjoy creating storyboards! 🎬
EOF

# Calculate the size needed for the DMG
echo "📏 Calculating DMG size..."
SIZE=$(du -sm "$TEMP_DIR" | cut -f1)
SIZE=$((SIZE + 50))  # Add 50MB buffer

echo "💾 Creating compressed DMG with ${SIZE}MB..."

# Create the DMG directly in compressed format
hdiutil create -srcfolder "$TEMP_DIR" -volname "$APP_NAME" -fs HFS+ \
    -fsargs "-c c=64,a=16,e=16" -format UDZO -imagekey zlib-level=9 "$DMG_PATH"

# Clean up
echo "🧹 Cleaning up..."
rm -rf "$TEMP_DIR"

# Get final file size
FINAL_SIZE=$(du -h "$DMG_PATH" | cut -f1)

echo "✅ DMG created successfully!"
echo "📁 File: $DMG_PATH"
echo "📏 Size: $FINAL_SIZE"
echo ""
echo "🎉 Your Storyboard Generator DMG is ready for distribution!"
echo "Users can now download and install the app by:"
echo "1. Double-clicking the DMG file"
echo "2. Dragging the app to Applications"
echo "3. Setting up their OpenAI API key"
echo "4. Launching the app"
