import streamlit as st
from storyboard import generate_storyboard, ImageModel, AspectRatio, Style
import asyncio
import io
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


DEFAULT_IMAGE_MODEL = ImageModel.OPENAI_DALLE_3
DEFAULT_ASPECT_RATIO = AspectRatio.WIDESCREEN
DEFAULT_STYLE = Style.SKETCHY_BW_GRAPHIC


def set_progress_callback(message):
    """Progress callback for Streamlit"""
    st.session_state.status = message


def generate_storyboard_streamlit(description, expectations, image_model, nb_steps, aspect_ratio, style, style_description, avoided_terms):
    """Generate storyboard with Streamlit progress updates"""
    try:
        expectations_list = expectations.split("\n") if expectations else []
        avoided_terms_list = avoided_terms if avoided_terms else []

        prs = asyncio.run(generate_storyboard(
            set_progress_callback,
            description,
            expectations_list,
            ImageModel[image_model],
            nb_steps,
            AspectRatio[aspect_ratio],
            Style[style],
            style_description,
            avoided_terms_list
        ))

        # Save to bytes buffer
        buffer = io.BytesIO()
        prs.save(buffer)
        buffer.seek(0)
        return buffer.getvalue()

    except Exception as e:
        st.error(f"An error occurred: {e}")
        return None


def main():
    st.set_page_config(
        page_title="Storyboard Generation",
        layout="wide",
        page_icon="💬",
        initial_sidebar_state="expanded"  # Always open sidebar
    )

    # Initialize session state
    if 'status' not in st.session_state:
        st.session_state.status = "Idle"
    if 'avoided_terms' not in st.session_state:
        st.session_state.avoided_terms = []

    # Check if OpenAI API key is available
    openai_key = os.getenv("OPENAI_KEY")
    if not openai_key:
        st.error("⚠️ OPENAI_KEY not found in environment variables. Please add it to your .env file.")
        st.stop()

    st.title("Storyboard Generation", anchor=False)

    # Settings in sidebar (always open)
    with st.sidebar:
        st.header("⚙️ Settings")

        # Image model selection
        image_model = st.selectbox(
            "Image model",
            options=[model.name for model in ImageModel],
            format_func=lambda x: ImageModel[x].value,
            index=0
        )

        # Number of steps
        nb_steps = st.number_input(
            "Number of steps",
            min_value=1,
            max_value=30,
            step=1,
            value=None,
            help="Leave empty for automatic step count"
        )

        # Aspect ratio
        aspect_ratio = st.selectbox(
            "Image aspect ratio",
            options=[ratio.name for ratio in AspectRatio],
            format_func=lambda x: AspectRatio[x].value,
            index=0
        )

        # Style
        style = st.selectbox(
            "Style",
            options=[s.name for s in Style],
            format_func=lambda x: Style[x].value,
            index=0
        )

        # Style description (only show if Custom is selected)
        style_description = ""
        if style == Style.CUSTOM.name:
            style_description = st.text_input("Custom style description")

        # st.divider()

        # Terms to avoid section in sidebar
        st.subheader("🚫 Terms to Avoid")

        # Add term functionality
        new_term = st.text_input("Add term to avoid", key="new_term_input")
        if st.button("Add term", use_container_width=True):
            if new_term and new_term not in st.session_state.avoided_terms:
                st.session_state.avoided_terms.append(new_term)
                del st.session_state["new_term_input"]
                st.session_state["new_term_input"] = ""
                st.rerun()

        # Display avoided terms
        if st.session_state.avoided_terms:
            avoided_terms = st.multiselect(
                "Selected terms to avoid",
                options=st.session_state.avoided_terms,
                default=st.session_state.avoided_terms
            )
        else:
            avoided_terms = []

        st.divider()

        # Reset button in sidebar
        if st.button("🔄 Reset All", use_container_width=True):
            st.session_state.avoided_terms = []
            st.rerun()

    # Main content area
    # Project description
    st.subheader("📝 Project Description", anchor=False)
    description = st.text_area(
        "Describe your project or product",
        height=120,
        placeholder="Enter a detailed description of your project, product, or service..."
    )

    # Insights
    st.subheader("💡 Insights & Expectations", anchor=False)
    expectations = st.text_area(
        "What insights should be leveraged in the story?",
        height=150,
        placeholder="Enter insights, expectations, or key points to include in the customer journey (one per line)..."
    )

    # Action buttons
    st.divider()

    col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 1])

    with col_btn1:
        generate_clicked = st.button("🚀 Generate Storyboard", type="primary", use_container_width=True)


    # Generate storyboard
    if generate_clicked:
        if not description:
            st.error("Please provide a project description")
        else:
            with st.spinner("Generating storyboard..."):
                st.session_state.status = "Generating..."

                pptx_data = generate_storyboard_streamlit(
                    description=description,
                    expectations=expectations,
                    image_model=image_model,
                    nb_steps=nb_steps,
                    aspect_ratio=aspect_ratio,
                    style=style,
                    style_description=style_description,
                    avoided_terms=avoided_terms
                )

                if pptx_data:
                    st.session_state.status = "Completed"
                    st.success("Storyboard generated successfully!")

                    # Download button
                    st.download_button(
                        label="Download Storyboard",
                        data=pptx_data,
                        file_name="storyboard.pptx",
                        mime="application/vnd.openxmlformats-officedocument.presentationml.presentation"
                    )
                else:
                    st.session_state.status = "Failed"


if __name__ == '__main__':
    main()
